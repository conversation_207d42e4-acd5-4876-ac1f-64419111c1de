using System.Data;
using System.Net;
using System.Runtime.CompilerServices;
using System.Text;
using CsvHelper;
using IdGen;
using Newtonsoft.Json;
using Ude;

namespace Sample.Benchmark;
using OfficeOpenXml;

public class SqlProcess
{

    private readonly IHttpClientFactory _httpClientFactory;

    public SqlProcess(IHttpClientFactory httpClientFactory)
    {
        _httpClientFactory = httpClientFactory;
    }

    #region Exoz订单采购折扣总额更新脚本

    public void ProcessSupplierOrderCostAmount()
    {
        var list = new List<CalcOrderDiscountAmount>();
        //读取本地excel文件
        var filePath = @"C:\Users\<USER>\Desktop\执行结果16.xlsx";
        FileInfo existingFile = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string orderId = worksheet.Cells[row, 1].Text; //A列
                //decimal newDiscountAmount =  decimal.Parse(worksheet.Cells[row, 4].Text); //D列
                decimal costPrice = decimal.Parse(worksheet.Cells[row, 6].Text); //F列
                decimal quantity = decimal.Parse(worksheet.Cells[row, 7].Text); //G列
                decimal costDiscountRate = decimal.Parse(worksheet.Cells[row, 8].Text); //H列
                var newDiscountAmount = Math.Round(costPrice * quantity * costDiscountRate / 100, 2);

                list.Add(new CalcOrderDiscountAmount
                {
                    BaseOrderId = orderId,
                    DiscountAmount = newDiscountAmount
                });
            }
        }

        var sqlList = new List<string>();
        foreach (var item in list)
        {
            var sql =
                $"UPDATE `orders`.`baseorder` SET `CostDiscountAmount` = {item.DiscountAmount}  WHERE `Id`  = {item.BaseOrderId};";
            sqlList.Add(sql);
        }

        //sqlList 写入本地txt文件
        var sqlFile = @"C:\Users\<USER>\Desktop\Exoz订单采购折扣总额更新脚本.txt";
        File.WriteAllLines(sqlFile, sqlList);
    }
    
    public class CalcOrderDiscountAmount
    {
        public string BaseOrderId { get; set; }

        public decimal DiscountAmount { get; set; }
    }

    #endregion

    #region 门票组合旧数据修复

    public void TicketCombinationProcess()
    {
        var combinationList = ReadCombinationFile();
        var settingList = ReadSerSettingFile();

        var packageSqlList = new List<string>();
        var packageItemSqlList = new List<string>();

        foreach (var combinationItem in combinationList)
        {
            var now = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            var packageSql =
                $"INSERT INTO `scenic`.`ticketscombinationpackage` (`Id`, `TicketsCombinationId`, `Name`, `CreateTime`, `UpdateTime`, `TenantId`, `PackageType`, `TimeSlotName`) VALUES ({combinationItem.Id}, {combinationItem.Id}, '{combinationItem.Name + "全天"}', '{now}', NULL, {combinationItem.TenantId}, 1, NULL);";
            packageSqlList.Add(packageSql);
            
            var relatedSettings = settingList.Where(x => x.TicketsCombinationId == combinationItem.Id).ToList();
            foreach (var setting in relatedSettings)
            {
                var packageItemId = CreateSnowId();
                var packageItemSql = $"INSERT INTO `scenic`.`ticketscombinationpackageitem` (`Id`, `TicketsCombinationId`, `TicketsCombinationPackageId`, `TicketsId`, `TenantId`, `CreateTime`, `Quantity`, `TimeSlotId`, `UpdateTime`) VALUES ({packageItemId}, {combinationItem.Id}, {combinationItem.Id}, {setting.TicketsId}, {combinationItem.TenantId}, '{now}', {setting.Quantity}, {(setting.TimeSlotId?.ToString() ?? "NULL")}, NULL);";
                packageItemSqlList.Add(packageItemSql);
            }
        }
        
        //sqlList 写入本地txt文件
        var packageSqlFile = @"C:\Users\<USER>\Desktop\组合套餐脚本.txt";
        File.WriteAllLines(packageSqlFile, packageSqlList);
        
        var packageItemSqlFile = @"C:\Users\<USER>\Desktop\组合套餐配置项脚本.txt";
        File.WriteAllLines(packageItemSqlFile,packageItemSqlList);
    }

    private List<Combination> ReadCombinationFile()
    {
        var result = new List<Combination>();
        var combinationFilePath = @"C:\Users\<USER>\Desktop\组合产品表数据.xlsx";
        FileInfo existingFile = new FileInfo(combinationFilePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                long id = long.Parse(worksheet.Cells[row, 1].Text); //A列
                string name = worksheet.Cells[row, 2].Text; //B列
                long tenantId = long.Parse(worksheet.Cells[row, 6].Text); //F列
                
                result.Add(new Combination
                {
                    Id = id,
                    Name = name,
                    TenantId = tenantId
                });
            }
        }
        
        return result;
    }

    private List<Setting> ReadSerSettingFile()
    {
        var result = new List<Setting>();
        var settingFilePath = @"C:\Users\<USER>\Desktop\组合产品配置池表数据.xlsx";
        FileInfo existingFile = new FileInfo(settingFilePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                long ticketsCombinationId = long.Parse(worksheet.Cells[row, 2].Text); //B列
                long ticketId = long.Parse(worksheet.Cells[row, 3].Text); //C列
                int quantity = Convert.ToInt32(worksheet.Cells[row, 4].Text); //D列
                string timesloIdText = worksheet.Cells[row, 8].Text; //H列
                long? timesloId = !string.IsNullOrEmpty(timesloIdText) ? long.Parse(timesloIdText) : null;
                
                result.Add(new Setting
                {
                    TicketsCombinationId = ticketsCombinationId,
                    TicketsId = ticketId,
                    Quantity = quantity,
                    TimeSlotId = timesloId
                });
            }
        }

        return result;
    }

    private class Combination
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public long TenantId { get; set; }
    }
    
    private class Setting
    {
        public long TicketsCombinationId { get; set; }
        public long TicketsId { get; set; }
        public long? TimeSlotId { get; set; }
        public int Quantity { get; set; }
    }

    #endregion

    #region API供应商产品配置修复

    public void ApiSupplierProductConfigRepair()
    {
        var apiSupplierProductConfigList = ReadApiSupplierProductConfigFile();

        var sqlList = new List<string>();
        foreach (var item in apiSupplierProductConfigList)
        {
            if (!string.IsNullOrEmpty(item.PackageId_New) && !string.IsNullOrEmpty(item.SkuId_New))
            {
                var sql =
                    $"UPDATE `scenic`.`tickets` SET `PackageId` ='{item.PackageId_New}',`SkuId` = '{item.SkuId_New}' WHERE `PriceInventorySource` = 7 and `ActivityId` ='{item.ActivityId}' and `PackageId` ='{item.PackageId_Old}' and `SkuId` ='{item.SkuId_Old}'; ";
                sqlList.Add(sql);
            }
            
            if (!string.IsNullOrEmpty(item.PackageId_New) && string.IsNullOrEmpty(item.SkuId_New))
            {
                var sql =
                    $"UPDATE `scenic`.`tickets` SET `PackageId` ='{item.PackageId_New}' WHERE `PriceInventorySource` = 4 and `ActivityId` ='{item.ActivityId}' and `PackageId` ='{item.PackageId_Old}' and `SkuId` ='{item.SkuId_Old}'; ";
                sqlList.Add(sql);
            }
            
            if (string.IsNullOrEmpty(item.PackageId_New) && !string.IsNullOrEmpty(item.SkuId_New))
            {
                var sql =
                    $"UPDATE `scenic`.`tickets` SET `SkuId` ='{item.SkuId_New}' WHERE `PriceInventorySource` = 8 and `ActivityId` ='{item.ActivityId}' and `SkuId` ='{item.SkuId_Old}'; ";
                sqlList.Add(sql);
            }
        }
        
        //sqlList 写入本地txt文件
        var sqlFile = @"C:\Users\<USER>\Desktop\门票EY供应端产品配置更新脚本.txt";
        File.WriteAllLines(sqlFile, sqlList);
    }
    
    private List<ApiSupplierProductConfig> ReadApiSupplierProductConfigFile()
    {
        var result = new List<ApiSupplierProductConfig>();
        var filePath = @"C:\Users\<USER>\Desktop\执行结果-eyounz.xlsx";
        FileInfo existingFile = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string activityId = worksheet.Cells[row, 1].Text; //A列
                string packageId_Old = worksheet.Cells[row, 2].Text; //B列
                string skuId_Old = worksheet.Cells[row, 3].Text; //C列
                string packageId_New = worksheet.Cells[row, 5].Text ?? string.Empty; //D列
                string skuId_New = worksheet.Cells[row, 4].Text  ?? string.Empty; //E列
                
                result.Add(new ApiSupplierProductConfig
                {
                    ActivityId = activityId,
                    PackageId_Old = packageId_Old,
                    PackageId_New = packageId_New,
                    SkuId_Old = skuId_Old,
                    SkuId_New = skuId_New
                });
            }
        }

        return result;
    }
    
    public class ApiSupplierProductConfig
    {
        public string ActivityId { get; set; }
        public string PackageId_Old { get; set; }
        public string PackageId_New { get; set; }
        public string SkuId_Old { get; set; }
        public string SkuId_New { get; set; }
    }

    #endregion

    #region ApiCity

    public void GetApiCity()
    {
        var getHopSource1 = GetHopSource1();
        var getHopSource2 = GetHopSource2();
        var getHopSource3 = GetHopSource3();
        var getHopSource = getHopSource1.Concat(getHopSource2).Concat(getHopSource3).ToList();
        var getApiCityList = GetApiCityList();
        var getCityList = GetCityList();
        var sqlList = new List<string>();
        foreach (var item in getApiCityList)
        {
            var hopSource = getHopSource.FirstOrDefault(x => x.CityCode == item.ApiCityCode.ToString());
            var cityEnName = hopSource?.CityEnName ?? string.Empty;
            if (hopSource == null)
            {
                cityEnName = getCityList.FirstOrDefault(x => x.CityCode == item.CityCode)?.ENName;
            }

            var escapedCityEnName  = cityEnName?.Replace("'", "''");            
            var sql = $"UPDATE `resource`.`apicity` SET `ApiCityEnName` = '{escapedCityEnName}' WHERE `ApiCityCode`  = {item.ApiCityCode};";
            sqlList.Add(sql);

            Console.WriteLine(item.Id);
        }
        
        // 保存sql脚本
        var sqlFile = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\源数据\hop\apicityEnName.txt";
        File.WriteAllLines(sqlFile, sqlList);
        
        
        // var requestUri = new Uri("http://hapi.test.huizhi-intl.com/saas/getcities?countryCode=" + countryCode);
        // var client = _httpClientFactory.CreateClient();
        // var responseMessage = await client.GetAsync(requestUri: requestUri);
        // responseMessage.EnsureSuccessStatusCode();
        // var responseContent = await responseMessage.Content.ReadAsStringAsync();
    }
    
    private List<HopSource> GetHopSource1()
    {
        var result = new List<HopSource>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\源数据\hop\hop1.csv";
        using (var reader = new StreamReader(filePath))
        {
            reader.ReadLine(); // 跳过表头
            while (!reader.EndOfStream)
            {
                var line = reader.ReadLine();
                var values = line.Split(',');
                //由于csv文件数据读取,需要移除前后的双引号
                values = values.Select(value => value.Trim('"')).ToArray();

                if (values[0] == "292371")
                {
                    
                }
        
                result.Add(new HopSource
                {
                    CityCode = values[6],
                    CityEnName = values[10]
                });
            }
        }

        result = result.GroupBy(x => x.CityCode)
            .Select(x => x.First())
            .ToList();
        return result;
    }
    private List<HopSource> GetHopSource2()
    {
        var result = new List<HopSource>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\源数据\hop\hop2.csv";
        using (var reader = new StreamReader(filePath))
        {
            reader.ReadLine(); // 跳过表头
            while (!reader.EndOfStream)
            {
                var line = reader.ReadLine();
                var values = line.Split(',');
                //由于csv文件数据读取,需要移除前后的双引号
                values = values.Select(value => value.Trim('"')).ToArray();

                if (values[0] == "292371")
                {
                    
                }
        
                result.Add(new HopSource
                {
                    CityCode = values[6],
                    CityEnName = values[10]
                });
            }
        }

        result = result.GroupBy(x => x.CityCode)
            .Select(x => x.First())
            .ToList();
        return result;
    }
    private List<HopSource> GetHopSource3()
    {
        var result = new List<HopSource>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\源数据\hop\hop3.csv";
        using (var reader = new StreamReader(filePath))
        {
            reader.ReadLine(); // 跳过表头
            while (!reader.EndOfStream)
            {
                var line = reader.ReadLine();
                var values = line.Split(',');
                //由于csv文件数据读取,需要移除前后的双引号
                values = values.Select(value => value.Trim('"')).ToArray();

                if (values[0] == "292371")
                {
                    
                }
        
                result.Add(new HopSource
                {
                    CityCode = values[6],
                    CityEnName = values[10]
                });
            }
        }

        result = result.GroupBy(x => x.CityCode)
            .Select(x => x.First())
            .ToList();
        return result;
    }

    private class HopSource
    {
        public string CityCode { get; set; }
        public string CityEnName { get; set; }
    }
    #endregion
    
    #region temporary

    public void Temporary()
    {
        var ids = new List<string>();
        var filePath = @"C:\Users\<USER>\Desktop\时段id.xlsx";
        FileInfo existingFile = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string id = worksheet.Cells[row, 1].Text; //A列
                ids.Add(id);
            }
        }
        
        var sqlList = new List<string>();
        foreach (var id in ids)
        {
            var sql =
                $"DELETE FROM `scenic`.`ticketscalendarprice` WHERE `TimeSlotId` = {id};";
            // var sql =
            //     $"DELETE FROM `product`.`lineproductskucalendarprice` WHERE `LineProductId` = {id};";
            sqlList.Add(sql);
        }

        //sqlList 写入本地txt文件
        var sqlFile = @"C:\Users\<USER>\Desktop\门票时段日历数据移除脚本.txt";
        File.WriteAllLines(sqlFile, sqlList);
    }

    #endregion

    #region GDS国家城市匹配

    public void MatchCity()
    {
        var countryList = GetCountryList();
        var provinceList = GetProvinceList();
        var cityList = GetCityList();
        var gdsData = GetGdsCityData();

        var maxCountryCode = countryList.Max(x => x.CountryCode);
        var maxProvinceCode = provinceList.Max(x => x.ProvinceCode);
        var maxCityCode = cityList.Max(x => x.CityCode);

        var countrySqlList = new List<string>();//国家表数据脚本
        var provinceSqlList = new List<string>();// 省份表数据脚本
        var citySqlList = new List<string>(); // 城市表数据脚本
        foreach (var gdsItem in gdsData)
        {
            #region 国家匹配

            var currentCountryCode = 0;
            var currentCountryZhName = gdsItem.CountryZhName;
            var country = countryList.FirstOrDefault(x => x.ENName == gdsItem.CountryEnName || x.ZHName == gdsItem.CountryZhName);
            if (country!=null)
            {
                // 国家不为空 延用数据
                currentCountryCode = country.CountryCode;
                currentCountryZhName = country.ZHName;
            }
            else
            {
                //  国家为空 新增数据
                maxCountryCode += 1; //刷新最大国家编号值
                currentCountryCode = maxCountryCode;

                var id = CreateSnowId();
                var sql = $"INSERT INTO `resource`.`country` (`Id`,`CountryCode`,`ZHName`,`ENName`,`IsoCode`) VALUES ({id},{currentCountryCode},'{gdsItem.CountryZhName}','{gdsItem.CountryEnName}','{gdsItem.IsoCode}');";
                countrySqlList.Add(sql);
                
                countryList.Add(new Country
                {
                    CountryCode = maxCountryCode,
                    ZHName = gdsItem.CountryZhName,
                    ENName = gdsItem.CountryEnName
                });
            }

            #endregion

            #region 省份匹配

            var currentProvinceCode = 0;
            var province = provinceList.FirstOrDefault(x => x.CountryCode == currentCountryCode && x.ZHName == currentCountryZhName);
            if (province != null)
            {
                // 省份不为空 延用数据
                currentProvinceCode = province.ProvinceCode;
            }
            else
            {
                // 省份为空 新增数据
                maxProvinceCode += 1; // 刷新最大省份编号值
                currentProvinceCode = maxProvinceCode;
                var id = CreateSnowId();
                var sql = $"INSERT INTO `resource`.`province` (`Id`,`CountryCode`,`ProvinceCode`,`ZHName`,`ENName`) VALUES ({id},{currentCountryCode},{currentProvinceCode},'{gdsItem.CountryZhName}','{gdsItem.CountryEnName}');";
                provinceSqlList.Add(sql);
                
                provinceList.Add(new Province
                {
                    CountryCode = currentCountryCode,
                    ProvinceCode = currentProvinceCode,
                    ZHName = gdsItem.CountryZhName,
                    ENName = gdsItem.CountryEnName
                });
            }

            #endregion
            
            #region 城市匹配
            
            var currentCityCode = 0;
            var city = cityList.FirstOrDefault(x => x.CountryCode == currentCountryCode && x.ProvinceCode == currentProvinceCode && x.ENName == gdsItem.CityEnName);
            if (city != null)
            {
                currentCityCode = city.CityCode;
            }
            else
            {
                maxCityCode += 1; // 刷新最大城市编号值
                currentCityCode = maxCityCode;
                var id = CreateSnowId();
                var escapedCityEnName  = gdsItem.CityEnName.Replace("'", "''");
                var sql = $"INSERT INTO `resource`.`city` (`Id`,`CountryCode`,`ProvinceCode`,`CityCode`,`ZHName`,`ENName`,`Location`,`CoordinateType`,`GooglePalceId`) VALUES ({id},{currentCountryCode},{currentProvinceCode},{currentCityCode},'{gdsItem.CityZhName}','{escapedCityEnName}',null,null,null);";
                citySqlList.Add(sql);
            }
            
            #endregion
        }
        
        //sqlList 写入本地txt文件
        var countrySqlFile = @"C:\Users\<USER>\OneDrive\文档\DGS城市数据文件\gds国家脚本_生产.txt";
        File.WriteAllLines(countrySqlFile, countrySqlList);
        
        var provinceSqlFile = @"C:\Users\<USER>\OneDrive\文档\DGS城市数据文件\gds省份脚本_生产.txt";
        File.WriteAllLines(provinceSqlFile, provinceSqlList);
        
        var citySqlFile = @"C:\Users\<USER>\OneDrive\文档\DGS城市数据文件\gds城市脚本_生产.txt";
        File.WriteAllLines(citySqlFile,citySqlList);
    }

    private List<GdsCityData> GetGdsCityData()
    {
        var result = new List<GdsCityData>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\DGS城市数据文件\gds.xlsx";
        FileInfo existingFile = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string? isoCode = worksheet.Cells[row, 1].Text;
                if (string.IsNullOrEmpty(isoCode))
                    isoCode = null;
                
                string countryEnName = worksheet.Cells[row, 2].Text; 
                string countryZhName = worksheet.Cells[row, 3].Text;
                string cityEnName = worksheet.Cells[row, 4].Text;
                string cityZhName = worksheet.Cells[row, 5].Text;
                
                result.Add(new GdsCityData
                {
                    CountryZhName = countryZhName,
                    CountryEnName = countryEnName,
                    CityZhName = cityZhName,
                    CityEnName = cityEnName,
                    IsoCode = isoCode
                });
            }
        }
        return result;
    }
    
    private List<Country> GetCountryList()
    {
        var result = new List<Country>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\DGS城市数据文件\country.xlsx";
        FileInfo existingFile = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string countryCode = worksheet.Cells[row, 2].Text; //B列
                string zhName = worksheet.Cells[row, 3].Text; //C列
                string enName = worksheet.Cells[row, 4].Text ?? string.Empty; //D列
                string isoCode = worksheet.Cells[row, 5].Text  ?? string.Empty; //E列
                
                result.Add(new Country
                {
                    CountryCode = Convert.ToInt32(countryCode),
                    ZHName = zhName,
                    ENName = enName,
                    IsoCode = isoCode
                });
            }
        }
        return result;
    }
    
    private List<Province> GetProvinceList()
    {
        var result = new List<Province>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\DGS城市数据文件\province.xlsx";
        FileInfo existingFile = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string countryCode = worksheet.Cells[row, 2].Text; //B列
                string provinceCode = worksheet.Cells[row, 3].Text; //C列
                string zhName = worksheet.Cells[row, 4].Text ?? string.Empty; //D列
                string enName = worksheet.Cells[row, 5].Text  ?? string.Empty; //E列
                
                result.Add(new Province
                {
                    CountryCode = Convert.ToInt32(countryCode),
                    ProvinceCode = Convert.ToInt32(provinceCode),
                    ZHName = zhName,
                    ENName = enName
                });
            }
        }
        return result;
    }
    
    private List<City> GetCityList()
    {
        var result = new List<City>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\DGS城市数据文件\city.xlsx";
        FileInfo existingFile = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string countryCode = worksheet.Cells[row, 2].Text; //B列
                string provinceCode = worksheet.Cells[row, 3].Text; //C列
                string cityCode = worksheet.Cells[row, 4].Text ?? string.Empty; //D列
                string zhName = worksheet.Cells[row, 5].Text  ?? string.Empty; //E列
                string enName = worksheet.Cells[row, 6].Text  ?? string.Empty; //F列
                
                result.Add(new City
                {
                    CountryCode = Convert.ToInt32(countryCode),
                    ProvinceCode = Convert.ToInt32(provinceCode),
                    CityCode = Convert.ToInt32(cityCode),
                    ZHName = zhName,
                    ENName = enName
                });
            }
        }
        return result;
    }

    private List<ApiCity> GetApiCityList()
    {
        var result = new List<ApiCity>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\DGS城市数据文件\apicity.xlsx";
        FileInfo existingFile = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string id = worksheet.Cells[row, 1].Text ?? string.Empty;
                string cityCode = worksheet.Cells[row, 2].Text ?? string.Empty;
                string apiCityCode = worksheet.Cells[row, 4].Text ?? string.Empty;
                string apiCityName = worksheet.Cells[row, 5].Text ?? string.Empty;
                
                result.Add(new ApiCity
                {
                    Id = id,
                    CityCode = Convert.ToInt32(cityCode),
                    ApiCityCode = Convert.ToInt32(apiCityCode),
                    ApiCityName = apiCityName
                });
            }
        }
        return result;
    }
    
    private class Country
    {
        public int CountryCode { get; set; }

        public string? ZHName { get; set; }

        public string? ENName { get; set; }
        
        public string? IsoCode { get; set; }
    }
    
    private class Province
    {
        public int CountryCode { get; set; }

        public int ProvinceCode { get; set; }

        public string ZHName { get; set; }

        public string ENName { get; set; }
    }
    
    private class City
    {
        public int CountryCode { get; set; }

        public int ProvinceCode { get; set; }

        public int CityCode { get; set; }

        public string ZHName { get; set; }

        public string ENName { get; set; }
    }
    
    private class ApiCity
    {
        public string Id { get; set; }
        public int CityCode { get; set; }
        public int ApiCityCode { get; set; }
        public string ApiCityName { get; set; }
    }
    
    private class GdsCityData
    {
        public string CountryZhName { get; set; }
        public string CountryEnName { get; set; }
        public string CityZhName { get; set; }
        public string CityEnName { get; set; }
        
        public string? IsoCode { get; set; }
    }

    #endregion

    #region HOP 数据修复

    public void HopDataRepair()
    {
        var saasResHotelData = GetSaasReSourceHotelV2();
        var saasSourceHotel = GetSaasSourceHotelXlsx()
            .Where(x=>x.CountryCode == "10")
            // .Where(x => !string.IsNullOrEmpty(x.CountryCnName))
            // .Where(x => !string.IsNullOrEmpty(x.CountryEnName))
            // .Where(x => !string.IsNullOrEmpty(x.CityCnName))
            // .Where(x => !string.IsNullOrEmpty(x.CityEnName))
            .ToList();
        var countryList = GetCountryList();
        var provinceList = GetProvinceList();
        var cityList = GetCityList();
        var apiCity = GetApiCityList();
        
        var hotelSqlList = new List<string>();
        var apiHotelSqlList = new List<string>();
        var newApiCitySqlList = new List<string>();
        var newCountrySqlList = new List<string>();
        var newProvinceSqlList = new List<string>();
        var newCitySqlList = new List<string>();

        var newSaasSourceHotel = new List<SaasOtherHotel>();
        foreach (var saasHotel in saasSourceHotel)
        {
            if(saasResHotelData.All(x => x.HopId != saasHotel.HopId))
                continue;
            
            // 判断hop关联国家城市编码数据与apiCity数据匹配
            var relatedApiCity = apiCity
                .FirstOrDefault(x => x.ApiCityCode.ToString() == saasHotel.CityCode);
            
            if (relatedApiCity != null )
            {
                var apiMatchSaasCity = cityList.FirstOrDefault(x => x.CityCode == relatedApiCity.CityCode);
                if (apiMatchSaasCity == null)
                {
                    //saasHotel.ApiCityMatch = "已匹配.但匹配Saas城市数据不存在";
                    saasHotel.ApiMatchType = ApiMatchType.ApiCityMatchButDataError;
                }
                else
                {
                    //saasHotel.ApiCityMatch = $"({apiMatchSaasCity.CityCode}/{apiMatchSaasCity.ZHName}/{apiMatchSaasCity.ENName})";
                    //saasHotel.SaasCityMath = saasHotel.ApiCityMatch;
                    saasHotel.ApiMatchType = ApiMatchType.ApiCityMatchSuccess;
                    saasHotel.SaaSCity = apiMatchSaasCity;
                }
            }
            else
            {
                // 判断hop关联国家城市编码数据与saas国家城市数据匹配
                var relatedCountList = countryList.Where(x => !string.IsNullOrEmpty(x.ENName))
                    .Where(x => saasHotel.CountryEnName.Contains(x.ENName, StringComparison.OrdinalIgnoreCase) ||
                                saasHotel.CountryCnName.Contains(x.ZHName))
                    .ToList();

                var relatedCityList = new List<City>();
                if (saasHotel.CountryEnName == "China")
                {
                    relatedCityList = cityList
                        .Where(x => !string.IsNullOrEmpty(x.ZHName))
                        .Where(x => saasHotel.CityCnName.Contains(x.ZHName))
                        .ToList();
                }
                else
                {
                    // 其他国家
                    var foreignCityList = new List<City>();
                    // if (saasHotel.CityCnName == saasHotel.CityEnName)
                    // {
                    //     foreignCityList = cityList
                    //         .Where(x => !string.IsNullOrEmpty(x.ENName))
                    //         .Where(x => saasHotel.CityEnName.Contains(x.ENName,StringComparison.OrdinalIgnoreCase))
                    //         .ToList();
                    // }
                    // else
                    // {
                    //     foreignCityList = cityList
                    //         .Where(x => !string.IsNullOrEmpty(x.ENName))
                    //         .Where(x => saasHotel.CityEnName.Contains(x.ENName,StringComparison.OrdinalIgnoreCase))
                    //         .ToList();
                    // }
                    foreignCityList = cityList
                        .Where(x => !string.IsNullOrEmpty(x.ENName))
                        .Where(x => saasHotel.CityEnName.Contains(x.ENName, StringComparison.OrdinalIgnoreCase))
                        .ToList();


                    // 存在多条匹配 精准匹配
                    if (foreignCityList.Any())
                    {
                        foreach (var foreignItem in foreignCityList)
                        {
                            // 关联国家数据
                            var relatedCountItem =
                                relatedCountList.FirstOrDefault(x => x.CountryCode == foreignItem.CountryCode);
                            if (relatedCountItem == null) continue;

                            relatedCityList.Add(foreignItem);
                        }
                    }
                }

                if (relatedCityList.Any())
                {
                    //  判断匹配数据
                    // saasHotel.SaasCityMath = string.Empty;
                    // foreach (var relatedCityItem in relatedCityList)
                    // {
                    //     // saasHotel.SaasCityMath += $"({relatedCityItem.CityCode}/{relatedCityItem.ZHName}/{relatedCityItem.ENName})";
                    // }

                    // 只取一条匹配的数据
                    if (relatedCityList.Count != 1)
                    {
                        var batchMatchItem =
                            relatedCityList.FirstOrDefault(x => x.ENName == saasHotel.CityEnName);

                        if (batchMatchItem != null)
                        {
                            if (saasHotel.ApiMatchType == ApiMatchType.NonMatch)
                            {
                                saasHotel.ApiMatchType = ApiMatchType.SaasCityMatchSuccess;
                            }

                            saasHotel.SaaSCity = batchMatchItem;
                        }
                    }
                    else
                    {
                        saasHotel.ApiMatchType = ApiMatchType.SaasCityMatchSuccess;
                        saasHotel.SaaSCity = relatedCityList.First();
                    }
                }
            }

            var relatedSaasResHotels = saasResHotelData.Where(x => x.HopId == saasHotel.HopId)
                .Select(x=>x.Id)
                .ToList();
            saasHotel.ResourceIds = relatedSaasResHotels;
            
            newSaasSourceHotel.Add(saasHotel);
            Console.WriteLine(saasHotel.HopId);
        }

        var group = newSaasSourceHotel.GroupBy(x => x.ApiCityMatch);

        // var errorSql = ApiCityMatchButDataError(saasSourceHotel.Where(x=>x.ApiMatchType == ApiMatchType.ApiCityMatchButDataError).ToList());
        // var sqlList1 = ProcessApiCityMatchSuccess(saasSourceHotel.Where(x=>x.ApiMatchType == ApiMatchType.ApiCityMatchSuccess).ToList(),
        //     countryList,provinceList);
        // var sqlList2 = ProcessSaasCityMatchSuccess(saasSourceHotel.Where(x=>x.ApiMatchType == ApiMatchType.SaasCityMatchSuccess).ToList(),
        //     countryList,provinceList);
        // var sqlList3 = NoMatch(saasSourceHotel.Where(x =>
        //             x.ApiMatchType is ApiMatchType.NonMatch or ApiMatchType.ApiCityMatchButDataError)
        //         .ToList(),
        //     countryList, provinces: provinceList, cities: cityList);
        //
        // hotelSqlList = sqlList1.hotelSqlList
        //     .Concat(sqlList2.hotelSqlList)
        //     .Concat(sqlList3.hotelSqlList)
        //     .ToList();
        // newApiCitySqlList = 
        //     errorSql.Concat(sqlList2.apiCitySqlList)
        //     .Concat(sqlList3.apiCitySqlList)
        //     .ToList();
        // newCountrySqlList = sqlList3.countrySqlList;
        // newProvinceSqlList = sqlList3.provinceSqlList;
        // newCitySqlList = sqlList3.citySqlList;
        // apiHotelSqlList = sqlList1.apiHotelList
        //     .Concat(sqlList2.apiHotelSqlList)
        //     .Concat(sqlList3.apiHotelSqlList)
        //     .ToList();
        //
        // //sqlList 写入本地txt文件
        //  File.WriteAllLines(@"C:\Users\<USER>\OneDrive\文档\酒店数据文件\其他城市酒店更新脚本.txt", hotelSqlList);
        //  File.WriteAllLines(@"C:\Users\<USER>\OneDrive\文档\酒店数据文件\api城市匹配更新脚本.txt", newApiCitySqlList);
        //  File.WriteAllLines(@"C:\Users\<USER>\OneDrive\文档\酒店数据文件\新增国家脚本.txt", newCountrySqlList);
        //  File.WriteAllLines(@"C:\Users\<USER>\OneDrive\文档\酒店数据文件\新增省份脚本.txt", newProvinceSqlList);
        //  File.WriteAllLines(@"C:\Users\<USER>\OneDrive\文档\酒店数据文件\新增城市脚本.txt", newCitySqlList);
        //  File.WriteAllLines(@"C:\Users\<USER>\OneDrive\文档\酒店数据文件\更新ApiHotel城市数据脚本.txt", apiHotelSqlList);



        // 将[saasSourceHotel]生成csv
        // using (var writer = new StreamWriter(@"C:\Users\<USER>\OneDrive\文档\酒店数据文件\中国城市缺失省份信息数据.csv"))
        // using (var csv = new CsvWriter(writer, System.Globalization.CultureInfo.InvariantCulture))
        // {
        //     var exportData = saasSourceHotel.Where(x => x.ApiMatchType == ApiMatchType.NonMatch)
        //         .Where(x => x.CountryCode == "10")
        //         .GroupBy(x=>new {x.CountryCode,x.CountryCnName,x.CountryEnName,x.CityCode,x.CityCnName,x.CityEnName})
        //         .Select(x=>x.Key)
        //         .ToList();
        //     csv.WriteRecords(exportData);
        // }

    }

    public void ChinaHopHotelDataRepair()
    {
        var saasResHotelData = GetSaasReSourceHotelV2();
        var saasSourceHotel = GetSaasSourceHotelXlsx()
            .Where(x=>x.CountryCode == "10")
            .ToList();
        var countryList = GetCountryList();
        var provinceList = GetProvinceList();
        var cityList = GetCityList();
        var apiCity = GetApiCityList();
        var supplementCity = GetSupplementCityInfo()
            .GroupBy(x => new { x.CityCnName, x.ProvinceCnName })
            .Select(x => x.First())
            .ToList();
        
        var hotelSqlList = new List<string>();
        var apiHotelSqlList = new List<string>();
        var newApiCitySqlList = new List<string>();
        var newCountrySqlList = new List<string>();
        var newProvinceSqlList = new List<string>();
        var newCitySqlList = new List<string>();

        var newSaasSourceHotel = new List<SaasOtherHotel>();

        foreach (var resourceHotel in saasResHotelData)
        {
            var saasHotel = saasSourceHotel.FirstOrDefault(x => x.HopId == resourceHotel.HopId);
            if(saasHotel == null) continue;
            if (saasHotel.CountryEnName != "China") continue;
            
            
            // 判断hop关联国家城市编码数据与apiCity数据匹配
            var relatedApiCity = apiCity
                .FirstOrDefault(x => x.ApiCityCode.ToString() == saasHotel.CityCode);
            
            if (relatedApiCity != null )
            {
                var apiMatchSaasCity = cityList.FirstOrDefault(x => x.CityCode == relatedApiCity.CityCode);
                if (apiMatchSaasCity == null)
                {
                    saasHotel.ApiMatchType = ApiMatchType.ApiCityMatchButDataError;
                }
                else
                {
                    saasHotel.ApiMatchType = ApiMatchType.ApiCityMatchSuccess;
                    saasHotel.SaaSCity = apiMatchSaasCity;
                }
            }
            else
            {
                // 判断hop关联国家城市编码数据与saas国家城市数据匹配
                var relatedCountList = countryList.Where(x => !string.IsNullOrEmpty(x.ENName))
                    .Where(x => saasHotel.CountryEnName.Contains(x.ENName, StringComparison.OrdinalIgnoreCase) ||
                                saasHotel.CountryCnName.Contains(x.ZHName))
                    .ToList();
                
                var relatedCityList = cityList
                    .Where(x => !string.IsNullOrEmpty(x.ZHName))
                    .Where(x => saasHotel.CityCnName.Contains(x.ZHName))
                    .Where(x=>x.CountryCode == 10)
                    .ToList();

                if (saasHotel.CityCnName == "香格里拉")
                {
                    
                }

                if (relatedCityList.Any())
                {
                    // 只取一条匹配的数据
                    if (relatedCityList.Count != 1)
                    {
                        var batchMatchItem =
                            relatedCityList.FirstOrDefault(x => x.ZHName == saasHotel.CityCnName);

                        if (batchMatchItem != null)
                        {
                            if (saasHotel.ApiMatchType == ApiMatchType.NonMatch)
                            {
                                saasHotel.ApiMatchType = ApiMatchType.SaasCityMatchSuccess;
                            }

                            saasHotel.SaaSCity = batchMatchItem;
                        }
                    }
                    else
                    {
                        saasHotel.ApiMatchType = ApiMatchType.SaasCityMatchSuccess;
                        saasHotel.SaaSCity = relatedCityList.First();
                    }
                }
                else
                {
                    var relatedSupplementCity = supplementCity.FirstOrDefault(x => x.CityCnName == saasHotel.CityCnName);
                    if (relatedSupplementCity != null)
                    {
                        saasHotel.SupplementCityInfo = relatedSupplementCity;
                        saasHotel.ApiMatchType = ApiMatchType.SupplementDataMatch;
                    }
                }
            }
            
            saasHotel.ResourceIds.Add(resourceHotel.Id);
            
            newSaasSourceHotel.Add(saasHotel);
            Console.WriteLine(saasHotel.HopId);
        }
        
        var apiMatch = newSaasSourceHotel.Where(x => x.ApiMatchType == ApiMatchType.ApiCityMatchSuccess).ToList();
        var sassMatch = newSaasSourceHotel.Where(x => x.ApiMatchType == ApiMatchType.SaasCityMatchSuccess).ToList();
        var supplementMatch = newSaasSourceHotel.Where(x => x.ApiMatchType == ApiMatchType.SupplementDataMatch).ToList();
        var nomatch = newSaasSourceHotel.Where(x => x.ApiMatchType == ApiMatchType.NonMatch).ToList();
        
        
        using (var writer = new StreamWriter(@"C:\Users\<USER>\OneDrive\文档\酒店数据文件\中国城市数据缺失V2.csv"))
        using (var csv = new CsvWriter(writer, System.Globalization.CultureInfo.InvariantCulture))
        {
            var exportData = new List<ErrorChinaCityExportData>();
            // exportData = supplementCity.Select(x => 
            //         new ErrorChinaCityExportData
            //         {
            //             CountryCode = x.CountryCode,
            //             CountryCnName = x.CountryZhName,
            //             CountryEnName = x.CountryEnName,
            //             
            //             CityCode = x.CityCode,
            //             CityCnName = x.CityCnName,
            //             CityEnName = x.CityEnName,
            //             
            //             ProvinceCnName = x.ProvinceCnName,
            //             ProvinceEnName = x.ProvinceEnName
            //         })
            //     .ToList();
            
            var nomatchData = nomatch
                .Where(x => x.CountryCode == "10")
                .GroupBy(x=>new {x.CountryCode,x.CountryCnName,x.CountryEnName,x.CityCnName,x.CityEnName})
                .Select(x=> new  ErrorChinaCityExportData
                {
                    CountryCode = x.Key.CountryCode,
                    CountryCnName = x.Key.CountryCnName,
                    CountryEnName = x.Key.CountryEnName,
                        
                    CityCode = "0",
                    CityCnName = x.Key.CityCnName,
                    CityEnName = x.Key.CityEnName,
                        
                    ProvinceCnName = string.Empty,
                    ProvinceEnName = string.Empty
                })
                .ToList();
            
            exportData.AddRange(nomatchData);
            csv.WriteRecords(exportData);
        }
    }
    
    
    private class ErrorChinaCityExportData
    {
        public string CountryCode { get; set; }
        public string CountryCnName { get; set; }
        public string CountryEnName { get; set; }
        
        public string CityCode { get; set; }
        public string CityCnName { get; set; }
        public string CityEnName { get; set; }
        
        public string ProvinceCnName { get; set; }
        public string ProvinceEnName { get; set; }
    }

    private List<SaasOtherHotel> GetSaasSourceHotelCsv()
    {
        var result = new List<SaasOtherHotel>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\其他城市-分析结果数据_1.csv";
        using (var reader = new StreamReader(filePath))
        {
            reader.ReadLine(); // 跳过表头
            while (!reader.EndOfStream)
            {
                var line = reader.ReadLine();
                var values = line.Split(',');
                //由于csv文件数据读取,需要移除前后的双引号
                values = values.Select(value => value.Trim('"')).ToArray();
        
                result.Add(new SaasOtherHotel
                {
                    HopId = values[0],
                    CnName = values[1],
                    EnName = values[2],
                    SAAS_CityCode = values[3],
                    SAAS_CityCnName = values[4],
                    CountryCode = values[5],
                    CountryCnName = values[6],
                    CountryEnName = values[7],
                    CityCode = values[8],
                    CityCnName = values[9],
                    CityEnName = values[10]
                });
            }
        }
        return result;
    }

    private List<SaasOtherHotel> GetSaasSourceHotelXlsx()
    {
        var result = new List<SaasOtherHotel>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\其他城市-分析结果数据.xlsx";
        FileInfo existingFile = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string HopId = worksheet.Cells[row, 1].Text ?? string.Empty;
                string CnName = worksheet.Cells[row, 2].Text ?? string.Empty;
                string EnName = worksheet.Cells[row, 3].Text ?? string.Empty;
                string SAAS_CityCode = worksheet.Cells[row, 4].Text ?? string.Empty;
                string SAAS_CityCnName = worksheet.Cells[row, 5].Text ?? string.Empty;
                string CountryCode = worksheet.Cells[row, 6].Text ?? string.Empty;
                string CountryCnName = worksheet.Cells[row, 7].Text ?? string.Empty;
                string CountryEnName = worksheet.Cells[row, 8].Text ?? string.Empty;
                string CityCode = worksheet.Cells[row, 9].Text ?? string.Empty;
                string CityCnName = worksheet.Cells[row, 10].Text ?? string.Empty;
                string CityEnName = worksheet.Cells[row, 11].Text ?? string.Empty;
                
                // 特殊处理
                if (CountryCnName == "United States")
                {
                    CountryCnName = "美国";
                }
                
                if (CountryCnName == "United Kingdom")
                {
                    CountryCnName = "英国";
                }

                if (CountryCnName == "South Africa")
                {
                    CountryCnName = "南非";
                }
                
                
                result.Add(new SaasOtherHotel
                {
                    HopId = HopId,
                    CnName = CnName,
                    EnName = EnName,
                    SAAS_CityCode = SAAS_CityCode,
                    SAAS_CityCnName = SAAS_CityCnName,
                    CountryCode = CountryCode,
                    CountryCnName = CountryCnName,
                    CountryEnName = CountryEnName,
                    CityCode = CityCode,
                    CityCnName = CityCnName,
                    CityEnName = CityEnName,
                    ApiCityMatch = "无匹配",
                    SaasCityMath = "无匹配"
                });
            }
        }
        return result;
    }

    private List<SaasSourceHotel> GetSaasReSourceHotelCsv()
    {
        var result = new List<SaasSourceHotel>();
        var filePath1 = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\源数据\saas\saas1.csv";
        using (var reader = new StreamReader(filePath1))
        {
            reader.ReadLine(); // 跳过表头
            while (!reader.EndOfStream)
            {
                var line = reader.ReadLine();
                var values = line.Split(',');
                //由于csv文件数据读取,需要移除前后的双引号
                values = values.Select(value => value.Trim('"')).ToArray();
        
                result.Add(new SaasSourceHotel
                {
                    Id = values[0],
                    HopId = values[1]
                });
            }
        }
        
        var filePath2 = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\源数据\saas\saas2.csv";
        using (var reader = new StreamReader(filePath2))
        {
            reader.ReadLine(); // 跳过表头
            while (!reader.EndOfStream)
            {
                var line = reader.ReadLine();
                var values = line.Split(',');
                //由于csv文件数据读取,需要移除前后的双引号
                values = values.Select(value => value.Trim('"')).ToArray();
                if(values.Length <= 2) continue;
        
                result.Add(new SaasSourceHotel
                {
                    Id = values[0],
                    HopId = values[1]
                });
            }
        }
        return result;
    }

    private List<SaasSourceHotel> GetSaasReSourceHotelXlsx()
    {
        var result = new List<SaasSourceHotel>();
        var filePath1 = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\源数据\saas\saas1.xlsx";
        FileInfo existingFile1 = new FileInfo(filePath1);
        using (ExcelPackage package = new ExcelPackage(existingFile1))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();
        
            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;
        
            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string id = worksheet.Cells[row, 1].Text ?? string.Empty;
                string HopId = worksheet.Cells[row, 2].Text ?? string.Empty;
                string countryCode = worksheet.Cells[row, 5].Text ?? string.Empty;
        
                if (countryCode != "1") continue;
                
                result.Add(new SaasSourceHotel
                {
                    Id = id,
                    HopId = HopId
                });
            }
        }
        
        
        var filePath2 = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\源数据\saas\saas2.xlsx";
        FileInfo existingFile2 = new FileInfo(filePath2);
        using (ExcelPackage package = new ExcelPackage(existingFile2))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();
        
            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;
        
            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string id = worksheet.Cells[row, 1].Text ?? string.Empty;
                string HopId = worksheet.Cells[row, 2].Text ?? string.Empty;
                string countryCode = worksheet.Cells[row, 5].Text ?? string.Empty;
        
                if (countryCode != "1") continue;
                
                result.Add(new SaasSourceHotel
                {
                    Id = id,
                    HopId = HopId
                });
            }
        }
        
        return result;
    }

    private List<SaasSourceHotel> GetSaasReSourceHotelV2()
    {
        var result = new List<SaasSourceHotel>();
        var filePath3 = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\resource_hotel.xlsx";
        FileInfo existingFile3 = new FileInfo(filePath3);
        using (ExcelPackage package = new ExcelPackage(existingFile3))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string resourceId = worksheet.Cells[row, 1].Text ?? string.Empty;
                string hopId = worksheet.Cells[row, 2].Text ?? string.Empty;
                
                
                result.Add(new SaasSourceHotel
                {
                    Id = resourceId,
                    HopId = hopId
                });
            }
        }

        return result;
    }

    private List<SupplementCityInfo> GetSupplementCityInfo()
    {
        var result = new List<SupplementCityInfo>();
        var filePath = @"C:\Users\<USER>\OneDrive\文档\酒店数据文件\中国城市缺失省份信息数据(1).xlsx";
        FileInfo existingFile3 = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile3))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                string CountryCode = worksheet.Cells[row, 1].Text ?? string.Empty;
                string CountryCnName = worksheet.Cells[row, 2].Text ?? string.Empty;
                string CountryEnName = worksheet.Cells[row, 3].Text ?? string.Empty;
                string CityCode = worksheet.Cells[row, 4].Text ?? string.Empty;
                string CityCnName = worksheet.Cells[row, 5].Text ?? string.Empty;
                string CityEnName = worksheet.Cells[row, 6].Text ?? string.Empty;
                string ProvinceCnName = worksheet.Cells[row, 8].Text ?? string.Empty;
                string ProvinceEnName = worksheet.Cells[row, 9].Text ?? string.Empty;
                
                
                result.Add(new SupplementCityInfo
                {
                    CountryCode = CountryCode,
                    CountryZhName = CountryCnName,
                    CountryEnName = CountryEnName,
                    CityCode = CityCode,
                    CityCnName = CityCnName,
                    CityEnName = CityEnName,
                    ProvinceCnName = ProvinceCnName,
                    ProvinceEnName = ProvinceEnName
                });
            }
        }

        return result;
    }
    
    private class SaasOtherHotel
    {
        public string HopId { get; set; }
        public string CnName { get; set; }
        public string EnName { get; set; }
        public string SAAS_CityCode { get; set; }
        public string SAAS_CityCnName { get; set; }
        public string CountryCode { get; set; }
        public string CountryCnName { get; set; }
        public string CountryEnName { get; set; }
        public string CityCode { get; set; }
        public string CityCnName { get; set; }
        public string CityEnName { get; set; }

        public string ApiCityMatch { get; set; }
        
        public string SaasCityMath { get; set; }

        public ApiMatchType ApiMatchType { get; set; } = ApiMatchType.NonMatch;

        public City? SaaSCity { get; set; }

        public SupplementCityInfo? SupplementCityInfo { get; set; }

        public List<string> ResourceIds { get; set; } = new List<string>();
    }
    
    private class SaasSourceHotel
    {
        public string Id { get; set; }

        public string HopId { get; set; }
    }
    
    private class SupplementCityInfo
    {
        public string CountryCode { get; set; }
        public string CountryZhName { get; set; }
        public string CountryEnName { get; set; }

        public string CityCode { get; set; }
        public string CityCnName { get; set; }
        public string CityEnName { get; set; }
        
        public string ProvinceCnName { get; set; }
        public string ProvinceEnName { get; set; }
    }
    
    private enum ApiMatchType
    {
        /// <summary>
        /// 无匹配
        /// </summary>
        NonMatch = 0,
        
        /// <summary>
        /// ApiCity存在匹配关系
        /// </summary>
        ApiCityMatchSuccess = 1,
        
        /// <summary>
        /// SaasCity存在匹配关系
        /// </summary>
        SaasCityMatchSuccess = 2,
        
        /// <summary>
        /// 存在ApiCity匹配关系,但数据错误
        /// </summary>
        ApiCityMatchButDataError = 3,
        
        SupplementDataMatch = 4
    }

    private List<string> ApiCityMatchButDataError(List<SaasOtherHotel> data)
    {
        var sqlList = new List<string>();
        foreach (var item in data.GroupBy(x=>x.CityCode))
        {
            var sql = $"DELETE FROM resource.apicity WHERE ApiCityCode = {item.Key};";
            sqlList.Add(sql);
        }

        return sqlList;
    }
    
    private (List<string> hotelSqlList,List<string> apiHotelList) ProcessApiCityMatchSuccess(List<SaasOtherHotel> data,List<Country> countries,List<Province> provinces)
    {
        // 修改hop关联saas国家城市编码数据
        var hotelSqlList = new List<string>();
        var apiHotelSqlList = new List<string>();
        foreach (var item in data)
        {
            var country = countries.First(x => x.CountryCode == item.SaaSCity.CountryCode);
            var province = provinces.First(x => x.CountryCode == item.SaaSCity.CountryCode && x.ProvinceCode == item.SaaSCity.ProvinceCode);
            var escapedHotelEnName  = item.SaaSCity.ZHName.Replace("'", "''");
            var hotelSql =
                $"UPDATE `resource`.`hotel` SET `CountryCode` = {item.SaaSCity.CountryCode}, `CountryName` = '{country.ZHName}', `ProvinceCode` = {item.SaaSCity.ProvinceCode}, `ProvinceName` = '{province.ZHName}', `CityCode` = {item.SaaSCity.CityCode}, `CityName` = '{escapedHotelEnName}' WHERE `HopId` = {item.HopId};";
            hotelSqlList.Add(hotelSql);

            foreach (var resourceId in item.ResourceIds)
            {
                var apiHotelSql =
                    $"UPDATE `hotel`.`apihotel` SET `CountryCode` = {item.SaaSCity.CountryCode}, `CountryName` = '{country.ZHName}', `CityCode` = {item.SaaSCity.CityCode}, `CityName` = '{escapedHotelEnName}' WHERE `ResourceHotelId` = {resourceId};";
                apiHotelSqlList.Add(apiHotelSql);
            }
        }

        return (hotelSqlList,apiHotelSqlList);
    }

    private (List<string> hotelSqlList,List<string> apiCitySqlList,List<string> apiHotelSqlList) ProcessSaasCityMatchSuccess(List<SaasOtherHotel> data,List<Country> countries,List<Province> provinces)
    {
        // 新增api城市匹配关系
        // 修改hop关联saas国家城市编码数据
        var apiCitySqlList = new List<string>();
        var hotelSqlList = new List<string>();
        var apiCityList = new List<ApiCity>();
        var apiHotelSqlList = new List<string>();
        var dateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        foreach (var item in data)
        {
            var country = countries.First(x => x.CountryCode == item.SaaSCity.CountryCode);
            var province = provinces.First(x => x.CountryCode == item.SaaSCity.CountryCode && x.ProvinceCode == item.SaaSCity.ProvinceCode);
            var escapedHotelEnName  = item.SaaSCity.ZHName.Replace("'", "''");
            var hotelSql =
                $"UPDATE `resource`.`hotel` SET `CountryCode` = {item.SaaSCity.CountryCode}, `CountryName` = '{country.ZHName}', `ProvinceCode` = {item.SaaSCity.ProvinceCode}, `ProvinceName` = '{province.ZHName}', `CityCode` = {item.SaaSCity.CityCode}, `CityName` = '{escapedHotelEnName}' WHERE `HopId` = {item.HopId};";
            hotelSqlList.Add(hotelSql);

            #region apiHotel 关联更新

            foreach (var resourceId in item.ResourceIds)
            {
                var apiHotelSql =
                    $"UPDATE `hotel`.`apihotel` SET `CountryCode` = {item.SaaSCity.CountryCode}, `CountryName` = '{country.ZHName}', `CityCode` = {item.SaaSCity.CityCode}, `CityName` = '{escapedHotelEnName}' WHERE `ResourceHotelId` = {resourceId};";
                apiHotelSqlList.Add(apiHotelSql);
            }

            #endregion

            var int32Code = Convert.ToInt32(item.CityCode);
            if (apiCityList.All(x => x.ApiCityCode != int32Code))
            {
                apiCityList.Add(new ApiCity
                {
                    ApiCityCode = int32Code,
                    ApiCityName = item.CityCnName,
                    CityCode = item.SaaSCity.CityCode
                });
            }
        }

        foreach (var apiCityItem in apiCityList)
        {
            var id = CreateSnowId();
            var escapedApiCityEnName  = apiCityItem.ApiCityName.Replace("'", "''");
            var apiCitySql =
                $"INSERT INTO `resource`.`apicity` (`Id`, `CityCode`, `ApiCityType`, `ApiCityCode`, `ApiCityName`, `CreateTime`, `UpdateTime`, `ApiCountryCode`) VALUES ({id},{apiCityItem.CityCode} ,{1} ,{apiCityItem.ApiCityCode} , '{escapedApiCityEnName}', '{dateTime}', '{dateTime}', NULL);";
            apiCitySqlList.Add(apiCitySql);
        }
        return (hotelSqlList,apiCitySqlList,apiHotelSqlList);
    }

    private (List<string> hotelSqlList,List<string> apiCitySqlList,List<string> countrySqlList,List<string> provinceSqlList,List<string> citySqlList
        ,List<string> apiHotelSqlList) NoMatch(List<SaasOtherHotel> data,List<Country> countries,List<Province> provinces, List<City> cities)
    {
        // 新增 国家省份城市数据
        var countrySqlList = new List<string>();
        var provinceSqlList= new List<string>();
        var citySqlList = new List<string>();

        // 新增api城市匹配关系
        var apiCitySqlList = new List<string>();
        
        // 修改hop关联saas国家城市编码数据
        var hotelSqlList = new List<string>();
        
        // 修改apihotel
        var apiHotelSqlList = new List<string>();

        var countryList = countries;
        var provinceList = provinces;
        var cityList = cities;
        
        var maxCountryCode = countries.Max(x => x.CountryCode);
        var maxProvinceCode = provinces.Max(x => x.ProvinceCode);
        var maxCityCode = cities.Max(x => x.CityCode);
        
        var apiCityList = new List<ApiCity>();
        var dateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        
        foreach (var item in data)
        {
            //数据不全 临时屏蔽中国
            if(item.CountryEnName == "China") continue;
            
            #region 国家匹配

            var currentCountryCode = 0;
            var currentCountryZhName = item.CountryCnName;
            var currentCountryEnName = item.CountryEnName;
            Country? country = null;
            country = item.CountryEnName == "China"
                ? countryList.FirstOrDefault(x => x.ZHName == "中国")
                : countryList.FirstOrDefault(x => x.ENName == item.CountryEnName);

            if (item.CountryEnName != "China" && country == null)
            {
                country = countryList.FirstOrDefault(x => x.ZHName == item.CountryCnName);
            }
            
            if (country!=null)
            {
                // 国家不为空 延用数据
                currentCountryCode = country.CountryCode;
                currentCountryZhName = country.ZHName;
                currentCountryEnName = country.ENName;
            }
            else
            {
                //  国家为空 新增数据
                maxCountryCode += 1; //刷新最大国家编号值
                currentCountryCode = maxCountryCode;

                var id = CreateSnowId();
                var escapedCountryEnName  = item.CountryEnName.Replace("'", "''");
                var sql = $"INSERT INTO `resource`.`country` (`Id`,`CountryCode`,`ZHName`,`ENName`,`IsoCode`) VALUES ({id},{currentCountryCode},'{item.CountryCnName}','{escapedCountryEnName}',NULL);";
                countrySqlList.Add(sql);
                
                countryList.Add(new Country
                {
                    CountryCode = maxCountryCode,
                    ZHName = item.CountryCnName,
                    ENName = item.CountryEnName
                });
            }

            #endregion

            #region 省份匹配

            if (item.CountryCnName == "韩国")
            {
                
            }
            
            var currentProvinceCode = 0;
            var currentProvinceZhName = item.CountryCnName;
            var province = item.CountryEnName == "China"
                ? provinceList.FirstOrDefault(x => x.CountryCode == currentCountryCode && x.ZHName == "")
                : provinceList.FirstOrDefault(x => x.CountryCode == currentCountryCode && (x.ENName == currentCountryEnName || x.ZHName == currentCountryZhName));
            if (province != null)
            {
                // 省份不为空 延用数据
                currentProvinceCode = province.ProvinceCode; 
                currentProvinceZhName = province.ZHName;
            }
            else
            {
                // 省份为空 新增数据
                maxProvinceCode += 1; // 刷新最大省份编号值
                currentProvinceCode = maxProvinceCode;
                var id = CreateSnowId();
                if (item.CountryEnName == "China")
                {
                    
                }
                else
                {
                    var escapedProvinceEnName  = item.CountryEnName.Replace("'", "''");
                    var sql = $"INSERT INTO `resource`.`province` (`Id`,`CountryCode`,`ProvinceCode`,`ZHName`,`ENName`) VALUES ({id},{currentCountryCode},{currentProvinceCode},'{item.CountryCnName}','{escapedProvinceEnName}');";
                    provinceSqlList.Add(sql);
                
                    provinceList.Add(new Province
                    {
                        CountryCode = currentCountryCode,
                        ProvinceCode = currentProvinceCode,
                        ZHName = item.CountryCnName,
                        ENName = item.CountryEnName
                    });
                }
            }

            #endregion
            
            #region 城市匹配
            
            var currentCityCode = 0;
            var currentCityZhName = item.CityCnName;
            var city = item.CountryEnName == "China"
                ? cityList.FirstOrDefault(x => x.CountryCode == currentCountryCode && x.ProvinceCode == currentProvinceCode && x.ZHName == item.CityCnName)
                : cityList.FirstOrDefault(x => x.CountryCode == currentCountryCode && x.ProvinceCode == currentProvinceCode && x.ENName == item.CityEnName);
            if (city != null)
            {
                currentCityCode = city.CityCode;
                currentCityZhName = city.ZHName;
            }
            else
            {
                maxCityCode += 1; // 刷新最大城市编号值
                currentCityCode = maxCityCode;
                var id = CreateSnowId();
                var escapedCityEnName  = item.CityEnName.Replace("'", "''");
                var escapedCityZhName  = item.CityCnName.Replace("'", "''");
                var sql = $"INSERT INTO `resource`.`city` (`Id`,`CountryCode`,`ProvinceCode`,`CityCode`,`ZHName`,`ENName`,`Location`,`CoordinateType`,`GooglePalceId`) VALUES ({id},{currentCountryCode},{currentProvinceCode},{currentCityCode},'{escapedCityZhName}','{escapedCityEnName}',null,null,null);";
                citySqlList.Add(sql);
                
                cityList.Add(new City
                {
                    CityCode = currentCityCode,
                    CountryCode = currentCountryCode,
                    ProvinceCode = currentProvinceCode,
                    ZHName = item.CityCnName,
                    ENName = item.CityEnName,
                });
            }
            
            #endregion

            #region hotel匹配

            var escapedHotelEnName  = currentCityZhName.Replace("'", "''");
            var hotelSql =
                $"UPDATE `resource`.`hotel` SET `CountryCode` = {currentCountryCode}, `CountryName` = '{currentCountryZhName}', `ProvinceCode` = {currentProvinceCode}, `ProvinceName` = '{currentProvinceZhName}', `CityCode` = {currentCityCode}, `CityName` = '{escapedHotelEnName}' WHERE `HopId` = {item.HopId};";
            hotelSqlList.Add(hotelSql);

            foreach (var resourceId in item.ResourceIds)
            {
                var apiHotelSql =
                    $"UPDATE `hotel`.`apihotel` SET `CountryCode` = {currentCountryCode}, `CountryName` = '{currentCountryZhName}', `CityCode` = {currentCityCode}, `CityName` = '{escapedHotelEnName}' WHERE `ResourceHotelId` = {resourceId};";
                apiHotelSqlList.Add(apiHotelSql);
            }

            #endregion
            
            var int32Code = Convert.ToInt32(item.CityCode);
            if (apiCityList.All(x => x.ApiCityCode != int32Code))
            {
                apiCityList.Add(new ApiCity
                {
                    ApiCityCode = int32Code,
                    ApiCityName = currentCityZhName,
                    CityCode = currentCityCode
                });
            }
        }
        
        
        #region api城市匹配

        foreach (var apiCityItem in apiCityList)
        {
            var id = CreateSnowId();
            var escapedApiCityEnName  = apiCityItem.ApiCityName.Replace("'", "''");
            var apiCitySql =
                $"INSERT INTO `resource`.`apicity` (`Id`, `CityCode`, `ApiCityType`, `ApiCityCode`, `ApiCityName`, `CreateTime`, `UpdateTime`, `ApiCountryCode`) VALUES ({id},{apiCityItem.CityCode} ,{1} ,{apiCityItem.ApiCityCode} , '{escapedApiCityEnName}', '{dateTime}', '{dateTime}', NULL);";
            apiCitySqlList.Add(apiCitySql);
        }

        #endregion

        return (hotelSqlList, apiCitySqlList, countrySqlList, provinceSqlList, citySqlList,apiHotelSqlList);
    }
    
    #endregion

    #region apihotel 租户权重置顶

    public void CreateTenantApiHotelSql()
    {
         var fileData = ReadUpdateTenantApiHotelFile()
            .OrderByDescending(x => x.TenantId)
            .ToList();
        var sqlList = new List<string>();
        foreach (var item in fileData)
        {
            var id = CreateSnowId();
            var sql = $"INSERT INTO `hotel`.`apihoteltenantconfig` (`Id`, `ApiHotelId`, `ResourceHotelId`, `OnTop`, `WeightValue`, `TenantId`) VALUES ({id}, {item.ApiHotelId}, {item.ResourceHotelId}, {item.OnTop}, {item.WeightValue}, {item.TenantId});";
            sqlList.Add(sql);
        }
        //sqlList 写入本地txt文件
        var sqlFile = @"C:\Users\<USER>\Desktop\租户apihotel权重置顶数据脚本.txt";
        File.WriteAllLines(sqlFile, sqlList);
    }
    
    private List<UpdateTenantApiHotel> ReadUpdateTenantApiHotelFile()
    {
        var result = new List<UpdateTenantApiHotel>();
        var filePath = @"C:\Users\<USER>\Desktop\apihotel.xlsx";
        FileInfo existingFile = new FileInfo(filePath);
        using (ExcelPackage package = new ExcelPackage(existingFile))
        {
            // 读取第一个工作表
            var ws = package.Workbook.Worksheets;
            ExcelWorksheet worksheet = package.Workbook.Worksheets.FirstOrDefault();

            // 假设第一行是表头
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            // 遍历行
            for (int row = 2; row <= rows; row++) // 从第二行开始
            {
                var apiHotelId = long.Parse(worksheet.Cells[row, 1].Text); //A列
                var resourceHotelId = long.Parse(worksheet.Cells[row, 2].Text); //B列
                var onTop = int.Parse(worksheet.Cells[row, 3].Text); //C列
                var weightValue = long.Parse(worksheet.Cells[row, 4].Text); //D列
                var tenantId = long.Parse(worksheet.Cells[row, 5].Text); //E列
                
                result.Add(new UpdateTenantApiHotel
                {
                    ApiHotelId = apiHotelId,
                    ResourceHotelId = resourceHotelId,
                    TenantId = tenantId,
                    WeightValue = weightValue,
                    OnTop = onTop
                });
            }
        }

        return result;
    }

    public class UpdateTenantApiHotel
    {
        public long ApiHotelId { get; set; }
        public long ResourceHotelId { get; set; }
        public long TenantId { get; set; }
        public int OnTop { get; set; }
        public long WeightValue { get; set; }
    }

    #endregion

    public List<long> BatchCreateSnowId(int count)
    {
        var outPut = new List<long>();
        for (int i = 0; i < count; i++)
        {
            var id = CreateSnowId();
            outPut.Add(id);
            Console.WriteLine(id);
        }

        return outPut;
    }
    
    private static long CreateSnowId()
    {
        if (IdWorker.IdGenerator.TryCreateId(out long snowId) == false)
        {
            Thread.Sleep(1);
            if (IdWorker.IdGenerator.TryCreateId(out snowId) == false)
                throw new SequenceOverflowException("TryCreateId false");
        }
        return snowId;
    }
    
}