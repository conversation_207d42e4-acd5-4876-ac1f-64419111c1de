using System.Net;
using IdGen;

namespace Sample.Benchmark;

public sealed class IdWorker
{
    public static readonly IdGenerator IdGenerator;

    static IdWorker()
    {
        var generatorId = GetGeneratorId();
        IdGenerator = new IdGenerator(generatorId);
    }

    /// <summary>
    /// 获取一个10bit(0~1023)机器Id
    /// 雪花算法10位机器码的设定理论上可以有1024个节点，需要保证每台机器上的雪花ID生成器使用不同的机器码，否则会有ID重复的风险。
    /// 此处简单粗暴处理，docker中IP是顺序分配，集群中服务比较少，机器码重复概率不大；
    /// 就算机器码重复，业务并发比较小，65536/ms的产能，重复概率也比较小
    /// </summary>
    /// <returns></returns>
    private static int GetGeneratorId()
    {
        var ipAddress = GetInterAddress().First();
        var bytes = ipAddress.GetAddressBytes();
        if (BitConverter.IsLittleEndian)
            Array.Reverse(bytes); // 确保字节顺序是大端序
        uint ipInt = BitConverter.ToUInt32(bytes, 0);
        var generatorId = ipInt % 1024;
        Console.WriteLine($"IP:{ipAddress} uInt32:{ipInt} GeneratorId:{generatorId}");
        return (int)generatorId;
    }

    private static IEnumerable<IPAddress> GetInterAddress()
    {
        var ipAddress = System.Net.NetworkInformation.NetworkInterface
            .GetAllNetworkInterfaces()
            .SelectMany(s => s.GetIPProperties().UnicastAddresses)
            .Where(s => s.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork
                        && !IPAddress.IsLoopback(s.Address))
            .Select(s => s.Address)
            .ToList();
        return ipAddress;
    }
}