using Sample.Benchmark.OtaOrderCheck;

namespace Sample.Benchmark
{
    public class TestExport
    {
        public static void TestExportFunctionality()
        {
            // 创建测试数据
            var testData = new List<CheckResultOutPut>
            {
                new CheckResultOutPut
                {
                    ChannelOrderNo = "TEST001",
                    SaasOrderId = "SAAS001",
                    CheckResultTypes = new List<CheckResultType> { CheckResultType.SaasOrderAmountException },
                    AbnormalOrderDescription = "测试异常单"
                },
                new CheckResultOutPut
                {
                    ChannelOrderNo = "TEST002",
                    SaasOrderId = null,
                    CheckResultTypes = new List<CheckResultType> { CheckResultType.SaasOrderMissing },
                    AbnormalOrderDescription = "SAAS订单缺失"
                },
                new CheckResultOutPut
                {
                    ChannelOrderNo = "TEST003",
                    SaasOrderId = "SAAS003",
                    CheckResultTypes = new List<CheckResultType> 
                    { 
                        CheckResultType.SaasOrderAmountException, 
                        CheckResultType.SaasOrderStatusException 
                    },
                    AbnormalOrderDescription = "多重异常"
                }
            };

            // 测试导出功能
            var exporter = new OtaOrderCheckExcelExport();
            try
            {
                exporter.Export(testData, "测试导出结果");
                Console.WriteLine("导出测试成功完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }
    }
}
