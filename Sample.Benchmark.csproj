<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="BenchmarkDotNet" Version="0.14.0" />
        <PackageReference Include="CsvHelper" Version="33.1.0" />
        <PackageReference Include="EPPlus.Core" Version="1.5.4" />
        <PackageReference Include="IdGen" Version="3.0.3" />
        <PackageReference Include="MessagePack" Version="3.1.0" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="10.0.0-preview.5.25277.114" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
        <PackageReference Include="ToolGood.Words.Pinyin" Version="3.1.0.3" />
        <PackageReference Include="Ude.NetStandard" Version="1.2.0" />
    </ItemGroup>
</Project>
