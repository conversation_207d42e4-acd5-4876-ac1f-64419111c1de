using ToolGood.Words.Pinyin;

namespace Sample.Benchmark;

public class Pinyin
{
    public void Convert(string txt)
    { 
        string surname = SplitChineseName(txt).Surname;
        string givenName = SplitChineseName(txt).GivenName;
        
        string surnamePinyin = WordsHelper.GetPinyinForName(surname);
        string givenNamePinyin = WordsHelper.GetPinyinForName(givenName);

        Console.WriteLine( surnamePinyin + "  " + givenNamePinyin);
    }
    
    public static (string Surname, string GivenName) SplitChineseName(string chineseName)
    {
        // 复姓列表，按长度降序排列，优先匹配长复姓
        string[] compoundSurnames = new string[]
        {
            "欧阳","司马","上官","诸葛","东方","皇甫","尉迟","公孙","长孙","慕容","司徒","司空","夏侯","宇文","轩辕","令狐","钟离","闾丘","子车","亓官","司寇","南宫","百里","东郭","西门","南门","呼延","羊舌","微生","公冶","仲孙"
        };

        // 先匹配复姓
        var surname = compoundSurnames.FirstOrDefault(x => chineseName.StartsWith(x));
        if (surname != null)
        {
            return (surname, chineseName.Substring(surname.Length));
        }
        else
        {
            // 默认第一个字为姓，后面为名
            if (chineseName.Length >= 2)
                return (chineseName.Substring(0, 1), chineseName.Substring(1));
            else
                return (chineseName, ""); // 只有一个字的极端情况
        }
    }
}