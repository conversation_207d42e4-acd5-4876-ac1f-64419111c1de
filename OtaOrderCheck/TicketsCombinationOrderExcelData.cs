using OfficeOpenXml;

namespace Sample.Benchmark.OtaOrderCheck;

public class TicketsCombinationOrderExcelData
{
    public static List<TicketsCombinationOrderData> GetTicketsCombinationOrderData()
    {
        return ReadTicketsCombinationOrderExcel();
    }
    
    private static List<TicketsCombinationOrderData> ReadTicketsCombinationOrderExcel()
    {
        var orders = new List<TicketsCombinationOrderData>();
        var filePath = @"C:\Users\<USER>\Desktop\saas\TicketsCombinationOrder.xlsx";
        // Check if file exists
        if (!File.Exists(filePath))
        {
            Console.WriteLine($"文件不存在: {filePath}");
            return orders;
        }
        try
        {
            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                // Use FirstOrDefault() to safely get the first worksheet
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet == null)
                {
                    Console.WriteLine($"工作表不存在: {filePath}");
                    return orders;
                }
                
                // 从第二行开始读取数据
                for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    var channelOrderNo = worksheet.Cells[row, 1].Text;
                    orders.Add(new TicketsCombinationOrderData
                    {
                        ChannelOrderNo = channelOrderNo,
                        PaymentAmount = decimal.Parse(worksheet.Cells[row, 2].Text)
                    });
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"读取文件 {filePath} 时出错: {ex.Message}");
        }
        return orders;
    }
}

public class TicketsCombinationOrderData
{
    
    /// <summary>
    /// 渠道单号
    /// </summary>
    public string ChannelOrderNo { get; set; }
    
    /// <summary>
    /// 渠道订单金额
    /// </summary>
    public decimal PaymentAmount { get; set; }
}