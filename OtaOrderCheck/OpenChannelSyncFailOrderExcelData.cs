using OfficeOpenXml;

namespace Sample.Benchmark.OtaOrderCheck;

public class OpenChannelSyncFailOrderExcelData
{
    public static List<OpenChannelSyncFailOrderCheckData> GetOpenChannelSyncFailOrderData()
    {
        return OpenChannelSyncFailOrderExcel();
    }
    
    private static List<OpenChannelSyncFailOrderCheckData> OpenChannelSyncFailOrderExcel()
    {
        var orders = new List<OpenChannelSyncFailOrderCheckData>();
        var filePath = @"C:\Users\<USER>\Desktop\saas\OpenChannelSyncFailOrder.xlsx";
        if (!File.Exists(filePath))
        {
            Console.WriteLine($"文件不存在: {filePath}");
            return orders;
        }
        try
        {
            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                // Use FirstOrDefault() to safely get the first worksheet
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet == null)
                {
                    Console.WriteLine($"工作表不存在: {filePath}");
                    return orders;
                }
                
                // 从第二行开始读取数据
                for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    var channelOrderNo = worksheet.Cells[row, 1].Text;
                    var channelMasterOrderNo = worksheet.Cells[row, 2].Text;
                    orders.Add(new OpenChannelSyncFailOrderCheckData
                    {
                        ChannelOrderNo = channelOrderNo,
                        ChannelMasterOrderNo = channelMasterOrderNo,
                        PaymentAmount = decimal.Parse(worksheet.Cells[row, 3].Text)
                    });
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"读取Excel文件时发生错误: {filePath}, 错误信息: {ex.Message}");
        }

        return orders;
    }
}

public class OpenChannelSyncFailOrderCheckData
{
    /// <summary>
    /// ChannelOrderNo
    /// </summary>
    public string ChannelOrderNo { get; set; }

    /// <summary>
    /// ChannelMasterOrderNo
    /// </summary>
    public string ChannelMasterOrderNo { get; set; }
    
    /// <summary>
    /// 渠道订单金额
    /// </summary>
    public decimal PaymentAmount { get; set; }
}
