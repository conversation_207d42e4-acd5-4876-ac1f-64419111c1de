namespace Sample.Benchmark.OtaOrderCheck;

public class CheckResultOutPut
{
    /// <summary>
    /// 渠道订单号
    /// </summary>
    public string ChannelOrderNo { get; set; }
    
    /// <summary>
    /// saas 订单id
    /// </summary>
    public string? SaasOrderId { get; set; }
    
    /// <summary>
    /// 检查结果类型
    /// </summary>
    public List<CheckResultType> CheckResultTypes { get; set; } = new();
    
    /// <summary>
    /// 异常单描述
    /// </summary>
    public string AbnormalOrderDescription { get; set; }

    /// <summary>
    /// 来源文件名
    /// </summary>
    public string SourceFileName { get; set; }
}


/// <summary>
/// 订单比对检查结果
/// <value>saas订单缺失,saas订单金额异常,saas订单状态异常</value>
/// </summary>
public enum CheckResultType
{
    /// <summary>
    /// saas订单缺失
    /// </summary>
    SaasOrderMissing = 0,
    
    /// <summary>
    /// saas订单金额异常
    /// </summary>
    SaasOrderAmountException = 1,
    
    /// <summary>
    /// saas订单状态异常
    /// </summary>
    SaasOrderStatusException = 2,
}

/// <summary>
/// 产品类型
/// <value>门票,日游</value>
/// </summary>
public enum ProductType
{
    Ticket = 1,
    DayTrip = 2,
}