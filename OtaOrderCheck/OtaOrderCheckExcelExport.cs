using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

namespace Sample.Benchmark.OtaOrderCheck;

public class OtaOrderCheckExcelExport
{
    /// <summary>
    /// 导出检查结果到Excel文件
    /// </summary>
    /// <param name="checkResultOutPuts">检查结果数据</param>
    /// <param name="fieldName">文件名</param>
    public void Export(List<CheckResultOutPut> checkResultOutPuts, string fieldName)
    {
        var outPutPath = $@"C:\Users\<USER>\Desktop\{fieldName}.xlsx";

        try
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(outPutPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("检查结果");

                // 设置表头
                SetHeaders(worksheet);

                // 填充数据
                FillData(worksheet, checkResultOutPuts);

                // 设置样式
                SetStyles(worksheet, checkResultOutPuts.Count);

                // 保存文件
                var fileInfo = new FileInfo(outPutPath);
                package.SaveAs(fileInfo);

                Console.WriteLine($"导出成功: {outPutPath}");
                Console.WriteLine($"共导出 {checkResultOutPuts.Count} 条异常记录");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"导出Excel文件时发生错误: {outPutPath}, 错误信息: {ex.Message}");
            throw;
        }
    }
    /// <summary>
    /// 设置Excel表头
    /// </summary>
    /// <param name="worksheet">工作表</param>
    private void SetHeaders(ExcelWorksheet worksheet)
    {
        worksheet.Cells[1, 1].Value = "渠道订单号";
        worksheet.Cells[1, 2].Value = "SAAS订单ID";
        worksheet.Cells[1, 3].Value = "检查结果类型";
        worksheet.Cells[1, 4].Value = "异常单描述";
        worksheet.Cells[1, 5].Value = "来源文件名";
    }

    /// <summary>
    /// 填充数据到Excel
    /// </summary>
    /// <param name="worksheet">工作表</param>
    /// <param name="checkResultOutPuts">检查结果数据</param>
    private void FillData(ExcelWorksheet worksheet, List<CheckResultOutPut> checkResultOutPuts)
    {
        for (int i = 0; i < checkResultOutPuts.Count; i++)
        {
            var row = i + 2; // 从第2行开始，第1行是表头
            var result = checkResultOutPuts[i];

            worksheet.Cells[row, 1].Value = result.ChannelOrderNo;
            worksheet.Cells[row, 2].Value = result.SaasOrderId ?? "无";
            worksheet.Cells[row, 3].Value = GetCheckResultTypeText(result.CheckResultTypes);
            worksheet.Cells[row, 4].Value = result.AbnormalOrderDescription ?? "";
            worksheet.Cells[row, 5].Value = result.SourceFileName;
        }
    }

    /// <summary>
    /// 获取检查结果类型的文本描述
    /// </summary>
    /// <param name="checkResultTypes">检查结果类型列表</param>
    /// <returns>文本描述</returns>
    private string GetCheckResultTypeText(List<CheckResultType> checkResultTypes)
    {
        if (checkResultTypes == null || !checkResultTypes.Any())
            return "正常";

        var descriptions = checkResultTypes.Select(type => type switch
        {
            CheckResultType.SaasOrderMissing => "SAAS订单缺失",
            CheckResultType.SaasOrderAmountException => "SAAS订单金额异常",
            CheckResultType.SaasOrderStatusException => "SAAS订单状态异常",
            _ => "未知异常"
        });

        return string.Join(", ", descriptions);
    }

    /// <summary>
    /// 获取检查结果的详细文本描述
    /// </summary>
    /// <param name="checkResultTypes">检查结果类型列表</param>
    /// <returns>详细文本描述</returns>
    private string GetCheckResultDetailText(List<CheckResultType> checkResultTypes)
    {
        if (checkResultTypes == null || !checkResultTypes.Any())
            return "订单检查正常，无异常";

        var details = checkResultTypes.Select(type => type switch
        {
            CheckResultType.SaasOrderMissing => "在SAAS系统中未找到对应的订单记录",
            CheckResultType.SaasOrderAmountException => "SAAS订单金额与渠道订单金额不匹配",
            CheckResultType.SaasOrderStatusException => "SAAS订单状态与渠道订单状态不匹配",
            _ => "发生未知类型的异常"
        });

        return string.Join("; ", details);
    }
    /// <summary>
    /// 设置Excel样式
    /// </summary>
    /// <param name="worksheet">工作表</param>
    /// <param name="dataRowCount">数据行数</param>
    private void SetStyles(ExcelWorksheet worksheet, int dataRowCount)
    {
        var totalRows = dataRowCount + 1; // 包含表头
        var totalColumns = 5;

        // 设置表头样式
        using (var headerRange = worksheet.Cells[1, 1, 1, totalColumns])
        {
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
            headerRange.Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
            headerRange.Style.Border.BorderAround(ExcelBorderStyle.Thin);
            headerRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        }

        // 设置数据区域样式
        if (dataRowCount > 0)
        {
            using (var dataRange = worksheet.Cells[2, 1, totalRows, totalColumns])
            {
                dataRange.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                dataRange.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                dataRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                dataRange.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                dataRange.Style.Border.Right.Style = ExcelBorderStyle.Thin;
            }
        }

        // 自动调整列宽
        worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

        // 设置最小列宽
        for (int col = 1; col <= totalColumns; col++)
        {
            if (worksheet.Column(col).Width < 15)
                worksheet.Column(col).Width = 15;
        }

        // 设置特定列的宽度
        worksheet.Column(1).Width = 20; // 渠道订单号
        worksheet.Column(2).Width = 15; // SAAS订单ID
        worksheet.Column(3).Width = 25; // 检查结果类型
        worksheet.Column(4).Width = 20; // 异常单描述
        worksheet.Column(5).Width = 20; // 来源文件名
    }
}