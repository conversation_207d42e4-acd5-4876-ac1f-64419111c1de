using OfficeOpenXml;

namespace Sample.Benchmark.OtaOrderCheck;

public class SaasOrderExcelData
{
    public static List<SaasOrderCheckData> GetSaasOrderData()
    {
        return ReadSaasOrderExcel();
    }
    
    private static List<SaasOrderCheckData> ReadSaasOrderExcel()
    {
        var orders = new List<SaasOrderCheckData>();
        // Check if file exists
        var filePath = @"C:\Users\<USER>\Desktop\saas\BaseOrder.xlsx";
        if (!File.Exists(filePath))
        {
            Console.WriteLine($"文件不存在: {filePath}");
            return orders;
        }
        try
        {
            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                // Use FirstOrDefault() to safely get the first worksheet
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet == null)
                {
                    Console.WriteLine($"工作表不存在: {filePath}");
                    return orders;
                }
                
                // 从第二行开始读取数据，
                for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    var channelNoStr = worksheet.Cells[row, 2].Text;
                    var channelOrderNos = new List<string>();
                    if(!string.IsNullOrEmpty(channelNoStr))
                    {
                        channelOrderNos = channelNoStr.Split(',').ToList();
                    }
                    
                    var order = new SaasOrderCheckData
                    {
                        BaseOrderId = worksheet.Cells[row, 1].Text,
                        ChannelOrderNos = channelOrderNos,
                        OrderType = int.Parse(worksheet.Cells[row, 3].Text),
                        Status = (BaseOrderStatus)int.Parse(worksheet.Cells[row, 4].Text),
                        TotalAmount = decimal.Parse(worksheet.Cells[row, 5].Text),
                        DiscountAmount = decimal.Parse(worksheet.Cells[row, 6].Text),
                        PaymentAmount = decimal.Parse(worksheet.Cells[row, 7].Text),
                        PaymentCurrencyCode = worksheet.Cells[row, 8].Text
                    };
                    orders.Add(order);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"读取Excel文件时发生错误: {filePath}, 错误信息: {ex.Message}");
        }
        return orders;
    }
}

public class SaasOrderCheckData
{
    /// <summary>
    /// saas base order id
    /// </summary>
    public string BaseOrderId { get; set; }
    
    /// <summary>
    /// 售卖渠道单号
    /// </summary>
    public List<string> ChannelOrderNos { get; set; } = new();
    
    /// <summary>
    /// 订单类型
    /// </summary>
    public int OrderType { get; set; }
    
    /// <summary>
    /// 订单状态
    /// </summary>
    public BaseOrderStatus Status { get; set; }

    /// <summary>
    /// 订单总额 = DiscountAmount + Payment.Amount
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    public decimal PaymentAmount { get; set; }
    
    /// <summary>
    /// 订单支付币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }
}

public enum BaseOrderStatus
{
    /// <summary>
    /// 待支付
    /// </summary>
    WaitingForPay = 1,

    /// <summary>
    /// 待完成
    /// </summary>
    UnFinished = 2,

    /// <summary>
    /// 已完成
    /// </summary>
    Finished = 3,

    /// <summary>
    /// 已关闭
    /// </summary>
    Closed = 4,
}