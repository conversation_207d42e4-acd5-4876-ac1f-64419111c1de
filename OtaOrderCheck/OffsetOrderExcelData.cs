using OfficeOpenXml;

namespace Sample.Benchmark.OtaOrderCheck;

public class OffsetOrderExcelData
{
    public static List<OffsetOrderCheckData> GetOffsetOrderData()
    {
        return ReadOffsetOrderExcel();
    }
    
    private static List<OffsetOrderCheckData> ReadOffsetOrderExcel()
    {
        var orders = new List<OffsetOrderCheckData>();
        var filePath = @"C:\Users\<USER>\Desktop\saas\OffsetOrder.xlsx";
        // Check if file exists
        if (!File.Exists(filePath))
        {
            Console.WriteLine($"文件不存在: {filePath}");
            return orders;
        }
        try
        {
            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                // Use FirstOrDefault() to safely get the first worksheet
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet == null)
                {
                    Console.WriteLine($"工作表不存在: {filePath}");
                    return orders;
                }
                
                // 从第二行开始读取数据
                for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    var baseOrderId = worksheet.Cells[row, 1].Text;
                    var amount = worksheet.Cells[row, 2].Text;
                    orders.Add(new OffsetOrderCheckData
                    {
                        BaseOrderId = baseOrderId,
                        Amount = decimal.Parse(amount),
                    });
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"读取Excel文件时发生错误: {filePath}, 错误信息: {ex.Message}");
        }

        return orders;
    }
}

public class OffsetOrderCheckData
{
    /// <summary>
    /// saas base order id
    /// </summary>
    public string BaseOrderId { get; set; }

    /// <summary>
    /// 抵冲金额(追加和退款 用正负区分)
    /// </summary>
    public decimal Amount { get; set; }
}