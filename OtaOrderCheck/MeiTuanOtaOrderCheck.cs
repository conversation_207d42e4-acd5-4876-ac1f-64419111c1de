using OfficeOpenXml;

namespace Sample.Benchmark.OtaOrderCheck;

public class MeiTuanOtaOrderCheck
{
    public void Check()
    { 
        var meiTuanOrders = MeiTuanOrderExcel();
        var saasOrders = SaasOrderExcelData.GetSaasOrderData();
        var offsetOrders = OffsetOrderExcelData.GetOffsetOrderData();
        var syncFailOrders = OpenChannelSyncFailOrderExcelData.GetOpenChannelSyncFailOrderData();
        var ticketsCombinationOrders = TicketsCombinationOrderExcelData.GetTicketsCombinationOrderData();

        var checkResultOutPuts = new List<CheckResultOutPut>();
        foreach (var meiTuanOrder in meiTuanOrders)
        {
            /* if : 1. 通过渠道单号匹配saas订单(saas订单存在一个订单包含多条渠道单号)
             * 1.1 订单状态比对  渠道 已取消 => saas 已关闭  渠道 已消费 => saas 已完成  比对失败,标记异常
             * 1.2 订单金额比对  渠道 实际支付 => saas 实际销售  比对失败,标记异常
             *
             * else : 2. 通过渠道单号匹配syncFailOrders
             *
             * else : 3. 通过渠道单号匹配ticketsCombinationOrders
             *
             * else : 4. 无匹配.表示saas缺失记录,标记异常
             */
            
            var checkResultOutPut = new CheckResultOutPut
            {
                ChannelOrderNo = meiTuanOrder.ChannelOrderNo,
                CheckResultTypes = new List<CheckResultType>()
            };

            SaasOrderCheckData? relatedSaasOrder = null;
            var relatedSaasOrders = saasOrders.Where(x => x.ChannelOrderNos.Contains(meiTuanOrder.ChannelOrderNo)).ToList();
            if (relatedSaasOrders.Any())
            {
                foreach (var item in relatedSaasOrders)
                {
                    foreach (var channelOrderNo in item.ChannelOrderNos)
                    {
                        if(channelOrderNo == meiTuanOrder.ChannelOrderNo)
                        {
                            relatedSaasOrder = item;
                            break;
                        }
                    }
                }
            }

            if (relatedSaasOrder != null)
            {
                // 1.1 订单状态比对  渠道 已取消 => saas 已关闭  渠道 已消费 => saas 已完成  比对失败,标记异常
                if(meiTuanOrder.Status == OtaOrderStatus.Cancel)
                {
                    if(relatedSaasOrder.Status != BaseOrderStatus.Closed)
                    {
                        checkResultOutPut.CheckResultTypes.Add(CheckResultType.SaasOrderStatusException);
                    }
                }
                
                // 1.2 订单金额比对  渠道 实际支付 => saas 实际销售  比对失败,标记异常
                var relatedSaasOrderAmount = relatedSaasOrder.PaymentAmount;
                // 查询有无抵冲单
                var offsetOrder = offsetOrders.Where(x => x.BaseOrderId == relatedSaasOrder.BaseOrderId)
                    .Sum(x => x.Amount);
                relatedSaasOrderAmount += offsetOrder;
                if(meiTuanOrder.Amount != relatedSaasOrderAmount)
                {
                    checkResultOutPut.CheckResultTypes.Add(CheckResultType.SaasOrderAmountException);
                }
            }
            else
            {
                // 2. 通过渠道单号匹配syncFailOrders
                var syncFailOrder = syncFailOrders.FirstOrDefault(x => x.ChannelOrderNo == meiTuanOrder.ChannelOrderNo);
                if(syncFailOrder != null)
                {
                    if(meiTuanOrder.Amount != syncFailOrder.PaymentAmount) 
                    {
                        checkResultOutPut.CheckResultTypes.Add(CheckResultType.SaasOrderAmountException);
                        checkResultOutPut.AbnormalOrderDescription = "异常单";
                    }
                }
                else
                {
                    // 3. 通过渠道单号匹配ticketsCombinationOrders
                    var ticketsCombinationOrder = ticketsCombinationOrders.FirstOrDefault(x => x.ChannelOrderNo == meiTuanOrder.ChannelOrderNo);
                    if(ticketsCombinationOrder != null)
                    {
                        if(meiTuanOrder.Amount != ticketsCombinationOrder.PaymentAmount) 
                        {
                            checkResultOutPut.CheckResultTypes.Add(CheckResultType.SaasOrderAmountException);
                            checkResultOutPut.AbnormalOrderDescription = "组合异常单";
                        }
                    }
                    else
                    {
                        checkResultOutPut.CheckResultTypes.Add(CheckResultType.SaasOrderMissing);
                    }
                }
            }

            if (checkResultOutPut.CheckResultTypes.Any())
            {
                checkResultOutPuts.Add(checkResultOutPut);
            }
        }
    }
    
    
    // 美团订单excel数据读取(18个excel.excel名称 : 1.xlsx~18.xlsx)
    private List<OtaOrderData> MeiTuanOrderExcel()
    {
        var folderPath = @"C:\Users\<USER>\Desktop\美团";
        var orders = new List<OtaOrderData>();
        for (int i = 1; i <= 18; i++)
        {
            var filePath = Path.Combine(folderPath, $"{i}.xlsx");
            orders.AddRange(ReadMeiTuanOrderExcel(filePath));
        }
        return orders;
    }

    private List<OtaOrderData> ReadMeiTuanOrderExcel(string filePath)
    {
        var orders = new List<OtaOrderData>();

        // Check if file exists
        if (!File.Exists(filePath))
        {
            Console.WriteLine($"文件不存在: {filePath}");
            return orders;
        }

        try
        {
            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                // Use FirstOrDefault() to safely get the first worksheet
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet == null)
                {
                    Console.WriteLine($"文件中没有工作表: {filePath}");
                    return orders;
                }

                // Check if worksheet has data
                if (worksheet.Dimension == null)
                {
                    Console.WriteLine($"工作表为空: {filePath}");
                    return orders;
                }

                var rowCount = worksheet.Dimension.Rows;
            for (int row = 2; row <= rowCount; row++)
            {
                // A1 : 渠道单号 '735660336102328' 去掉引号
                var channelOrderNo = worksheet.Cells[row, 1].Text.Trim('\'');
                // L1 : 实际支付金额
                var amount = decimal.Parse(worksheet.Cells[row, 12].Text);
                // S1 : 支付状态  `已支付`=> 正常  `未支付`=> 取消  `已退款`=> 退款
                var payStatus = worksheet.Cells[row, 19].Text;
                var status = payStatus switch
                {
                    "已支付" => OtaOrderStatus.Normal,
                    "未支付" => OtaOrderStatus.Cancel,
                    "已退款" => OtaOrderStatus.Refund,
                    _ => OtaOrderStatus.Normal,
                };
                var refundAmount = status == OtaOrderStatus.Refund ? amount : 0m;
                
                var order = new OtaOrderData
                {
                    ChannelOrderNo = channelOrderNo,
                    Status = status,
                    OtaChannelType = OtaChannelType.MeiTuan,
                    Amount = amount,
                    RefundAmount = refundAmount,
                };
                orders.Add(order);
            }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"读取Excel文件时发生错误: {filePath}, 错误信息: {ex.Message}");
        }

        return orders;
    }
    
}

/// <summary>
/// 渠道订单数据
/// </summary>
public class OtaOrderData
{
    /// <summary>
    /// 渠道单号
    /// </summary>
    public string ChannelOrderNo { get; set; }
        
    /// <summary>
    /// 订单状态
    /// </summary>
    public OtaOrderStatus Status { get; set; }

    /// <summary>
    /// 渠道类型
    /// </summary>
    public OtaChannelType OtaChannelType { get; set; }

    /// <summary>
    /// 订单金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 退款金额
    /// </summary>
    public decimal RefundAmount { get; set; }
}

/// <summary>
/// 渠道类型
/// <value>美团,携程,飞猪</value>
/// </summary>
public enum OtaChannelType
{
    /// <summary>
    /// 美团
    /// </summary>
    MeiTuan = 0,
    
    /// <summary>
    /// 携程
    /// </summary>
    Ctrip = 1,
    
    /// <summary>
    /// 飞猪
    /// </summary>
    FuiZi = 2,
}

/// <summary>
/// 渠道订单状态
/// </summary>
public enum OtaOrderStatus
{
    /// <summary>
    /// 正常
    /// </summary>
    Normal = 0,
        
    /// <summary>
    /// 取消
    /// </summary>
    Cancel = 1,
    
    /// <summary>
    /// 退款
    /// </summary>
    Refund = 2,
}