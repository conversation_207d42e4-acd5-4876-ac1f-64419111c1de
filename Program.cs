// See https://aka.ms/new-console-template for more information
using BenchmarkDotNet.Running;
using Microsoft.Extensions.DependencyInjection;
using OfficeOpenXml;
using Microsoft.Extensions.DependencyInjection;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Sample.Benchmark;
using Sample.Benchmark.OtaOrderCheck;

//BenchmarkRunner.Run<MessagePackVSJson>();

// var m = new MessagePackVSJson();
// m.TestSerialize();
// var test = Math.Round(Convert.ToDecimal(12.185), 2, MidpointRounding.ToEven);
// var test2 = Math.Round(Convert.ToDecimal(12.135), 2);
// var celling1 = (int)Math.Ceiling(6m/5);
// var celling2 = (int)Math.Floor(6m/5);
// var celling3 = 4 / 5;
// Console.WriteLine(celling1);
// Console.WriteLine(celling2);
// Console.WriteLine(celling3);

// 1. 创建 DI 容器
var services = new ServiceCollection();

// 2. 注册 HttpClient 服务
services.AddHttpClient();

// 3. 注册[类]
services.AddTransient<MeiTuanOtaOrderCheck>();

// 4. 构建服务提供者
var serviceProvider = services.BuildServiceProvider();

// 5. 使用服务（注意生命周期管理）
using (var scope = serviceProvider.CreateScope())
{
    var p = scope.ServiceProvider.GetRequiredService<MeiTuanOtaOrderCheck>();
    p.Check();
    //p.Convert("乌云其其格");
    //p.BatchCreateSnowId(5);
}