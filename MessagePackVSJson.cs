using BenchmarkDotNet.Attributes;

namespace Sample.Benchmark;

[MemoryDiagnoser,MarkdownExporter]
public class MessagePackVSJson
{
    #region 静态测试数据，避免每次测试动态生成

    private static readonly Message StaticTestData = GenerateStaticTestData(100_000);
    private static Message GenerateStaticTestData(int amount)
    {
        return new Message
        {
            TenantId = 1200000000000000000,
            OtherDatesPushEmpty = true,
            Items = Enumerable.Range(0, amount).Select(x => new MessageItem
            {
                ProductId = 1200000000000000000 + x,
                SkuId = 1200000000000000000 + x,
                Date = DateTime.Parse("2020-01-01"),
                TotalQuantity = 10,
                AvailableQuantity = 10
            }).ToList()
        };
    }

    #endregion

    [Params(10_000, 100_000)]
    public int Amount { get; set; }

    private Message testData = null!;

    [GlobalSetup]
    public void Setup()
    {
        // 根据指定的 Amount，从静态数据中生成适当规模的数据
        testData = new Message
        {
            TenantId = StaticTestData.TenantId,
            OtherDatesPushEmpty = StaticTestData.OtherDatesPushEmpty,
            Items = StaticTestData.Items.Take(Amount).ToList()
        };
    }

    [Benchmark]
    public void JsonSerialize()
    {
        var itemsJson = Newtonsoft.Json.JsonConvert.SerializeObject(testData.Items);
    }

    [Benchmark]
    public void MpackSerialize()
    {
        var itemsBytes = MessagePack.MessagePackSerializer.Typeless.Serialize(testData.Items);
    }

    [Benchmark]
    public void JsonSerializeAndDeserialize()
    {
        var itemsJson = Newtonsoft.Json.JsonConvert.SerializeObject(testData.Items);
        var items = Newtonsoft.Json.JsonConvert.DeserializeObject<List<MessageItem>>(itemsJson);
    }

    [Benchmark]
    public void MpackSerializeAndDeserialize()
    {
        var itemsBytes = MessagePack.MessagePackSerializer.Typeless.Serialize(testData.Items);
        var items = MessagePack.MessagePackSerializer.Typeless.Deserialize(itemsBytes) as List<MessageItem>;
    }

    public void TestDeserialize()
    {
        // 将 Base64 字符串转换为字节数组
        byte[] byteArray = Convert.FromBase64String("");

        // 使用 MessagePack 反序列化
        var hexString = MessagePack.MessagePackSerializer.ConvertToJson(byteArray);
        var result = MessagePack.MessagePackSerializer.Typeless.Deserialize(byteArray) as List<MessageItem>;
    }

    public void TestSerialize()
    {
        var list = new List<MessageItem>();
        var itemsBytes = MessagePack.MessagePackSerializer.Typeless.Serialize(list);
        var any = itemsBytes.Any();
    }
}

class Message
{
    public long TenantId { get; set; }
    public bool OtherDatesPushEmpty { get; set; }
    public List<MessageItem> Items { get; set; } = new();
}

class MessageItem
{
    public long ProductId { get; set; }
    public long SkuId { get; set; }
    public DateTime Date { get; set; }
    public int TotalQuantity { get; set; }
    public int AvailableQuantity { get; set; }
}