using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using BenchmarkDotNet.Attributes;

namespace Sample.Benchmark;

public partial class RouteTrie
{
    private readonly TrieNode _root = new();

    #region benchmark data

    private static readonly string[] _paths = [
        "/v1/user",
        "/v2/user",
        "/v2/agency",
        "/v1/user/add",
        "/v2/user/search"
    ];

    public RouteTrie()
    {
        Insert("/v1/user");
        Insert("/v1/agency");
        Insert("/v1/user/add");
        Insert("/v1/user/del");
        Insert("/v2/user/search");
    }

    #endregion

    public void Insert(string path)
    {
        var segments = GetSegments(path);
        TrieNode current = _root;
        foreach (var item in segments)
        {
            if (item == string.Empty)
            {
                current.EndIsDIR = true;
                break;
            }
            current.Children.TryAdd(item, new TrieNode());
            current = current.Children[item];
        }
    }

    [Benchmark]
    [Arguments("/v1/user")]
    [Arguments("/v2/user")]
    [Arguments("/v2/agency")]
    [Arguments("/v1/user/add")]
    [Arguments("/v2/user/search")]
    public bool PrefixMatch(string path)
    {
        //全匹配  "/"
        if (_root.EndIsDIR)
            return true;

        var segments = GetSegments(path);
        TrieNode current = _root;
        foreach (var item in segments)
        {
            if (current.Children.ContainsKey(item) == false)
                return false;

            current = current.Children[item];
            if (current.EndIsDIR)
                return true;
        }

        return true;
    }

    [Benchmark]
    [Arguments("/v1/user")]
    [Arguments("/v2/user")]
    [Arguments("/v2/agency")]
    [Arguments("/v1/user/add")]
    [Arguments("/v2/user/search")]
    public static bool ArrStartWith(string path)
    {
        return _paths.Any(x => path.StartsWith(x));
    }

    private static string[] GetSegments(string path)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(path);
        if (path.StartsWith('/') == false)
            throw new ArgumentException("must start with /");
        path = PathCleanRegex().Replace(path, "/");
        path = path[1..];
        var segments = path.Split('/');
        return segments;
    }

    [GeneratedRegex("/{2,}")]
    private static partial Regex PathCleanRegex();
}

public class TrieNode
{
    public Dictionary<string, TrieNode> Children { get; set; } = [];
    public bool EndIsDIR { get; set; } = false;
}