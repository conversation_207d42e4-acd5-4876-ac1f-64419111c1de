<PERSON>,Job,<PERSON><PERSON>ze<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,EvaluateOverhead,MaxAbsoluteError,MaxRelativeError,MinInvokeCount,MinIterationTime,OutlierMode,Affinity,EnvironmentVariables,Jit,LargeAddressAware,Platform,PowerPlanMode,Runtime,AllowVeryLargeObjects,Concurrent,CpuGroups,Force,HeapAffinitizeMask,HeapCount,NoAffinitize,RetainVm,Server,Arguments,BuildConfiguration,Clock,EngineFactory,NuGetReferences,Toolchain,IsMutator,InvocationCount,IterationCount,IterationTime,LaunchCount,MaxIterationCount,MaxWarmupIterationCount,MemoryRandomization,MinIterationCount,MinWarmupIterationCount,RunStrategy,UnrollFactor,WarmupCount,Amount,Mean,Error,StdDev,Median,Gen0,Gen1,Gen2,Allocated
JsonSerialize,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,1111111111111111,<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,X64,8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c,.NET 9.0,False,<PERSON>,False,<PERSON>,<PERSON>fault,Default,False,False,False,Default,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,<PERSON>fault,16,<PERSON>fault,10000,5.083 ms,0.0797 ms,0.0707 ms,5.074 ms,898.4375,820.3125,437.5000,6.24 MB
MpackSerialize,DefaultJob,False,Default,Default,Default,Default,Default,Default,1111111111111111,Empty,RyuJit,Default,X64,8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c,.NET 9.0,False,True,False,True,Default,Default,False,False,False,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,16,Default,10000,1.346 ms,0.0506 ms,0.1459 ms,1.274 ms,201.1719,201.1719,201.1719,1.58 MB
JsonSerializeAndDeserialize,DefaultJob,False,Default,Default,Default,Default,Default,Default,1111111111111111,Empty,RyuJit,Default,X64,8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c,.NET 9.0,False,True,False,True,Default,Default,False,False,False,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,16,Default,10000,17.712 ms,0.5170 ms,1.5082 ms,17.243 ms,1187.5000,1000.0000,500.0000,8.33 MB
MpackSerializeAndDeserialize,DefaultJob,False,Default,Default,Default,Default,Default,Default,1111111111111111,Empty,RyuJit,Default,X64,8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c,.NET 9.0,False,True,False,True,Default,Default,False,False,False,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,16,Default,10000,2.907 ms,0.1571 ms,0.4533 ms,2.787 ms,324.2188,289.0625,257.8125,2.12 MB
MpackSerializeAndDeserializeV2,DefaultJob,False,Default,Default,Default,Default,Default,Default,1111111111111111,Empty,RyuJit,Default,X64,8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c,.NET 9.0,False,True,False,True,Default,Default,False,False,False,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,16,Default,10000,NA,NA,NA,NA,NA,NA,NA,NA
JsonSerialize,DefaultJob,False,Default,Default,Default,Default,Default,Default,1111111111111111,Empty,RyuJit,Default,X64,8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c,.NET 9.0,False,True,False,True,Default,Default,False,False,False,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,16,Default,100000,69.560 ms,2.0161 ms,5.6864 ms,69.169 ms,6000.0000,3875.0000,1500.0000,62.3 MB
MpackSerialize,DefaultJob,False,Default,Default,Default,Default,Default,Default,1111111111111111,Empty,RyuJit,Default,X64,8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c,.NET 9.0,False,True,False,True,Default,Default,False,False,False,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,16,Default,100000,11.776 ms,0.2327 ms,0.3338 ms,11.808 ms,484.3750,484.3750,484.3750,15.83 MB
JsonSerializeAndDeserialize,DefaultJob,False,Default,Default,Default,Default,Default,Default,1111111111111111,Empty,RyuJit,Default,X64,8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c,.NET 9.0,False,True,False,True,Default,Default,False,False,False,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,16,Default,100000,178.753 ms,3.5128 ms,6.5979 ms,177.648 ms,8000.0000,4250.0000,1250.0000,82.62 MB
MpackSerializeAndDeserialize,DefaultJob,False,Default,Default,Default,Default,Default,Default,1111111111111111,Empty,RyuJit,Default,X64,8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c,.NET 9.0,False,True,False,True,Default,Default,False,False,False,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,16,Default,100000,26.440 ms,0.6105 ms,1.7808 ms,26.394 ms,1468.7500,1406.2500,906.2500,21.17 MB
MpackSerializeAndDeserializeV2,DefaultJob,False,Default,Default,Default,Default,Default,Default,1111111111111111,Empty,RyuJit,Default,X64,8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c,.NET 9.0,False,True,False,True,Default,Default,False,False,False,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,Default,16,Default,100000,NA,NA,NA,NA,NA,NA,NA,NA
