<!DOCTYPE html>
<html lang='en'>
<head>
<meta charset='utf-8' />
<title>Sample.Benchmark.MessagePackVSJson-20241224-144737</title>

<style type="text/css">
	table { border-collapse: collapse; display: block; width: 100%; overflow: auto; }
	td, th { padding: 6px 13px; border: 1px solid #ddd; text-align: right; }
	tr { background-color: #fff; border-top: 1px solid #ccc; }
	tr:nth-child(even) { background: #f8f8f8; }
</style>
</head>
<body>
<pre><code>
BenchmarkDotNet v0.14.0, Windows 11 (10.0.27764.1000)
AMD Ryzen 7 5800H with Radeon Graphics, 1 CPU, 16 logical and 8 physical cores
.NET SDK 9.0.100
  [Host]     : .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
  DefaultJob : .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
</code></pre>
<pre><code></code></pre>

<table>
<thead><tr><th>Method                  </th><th>Amount</th><th>Mean</th><th>Error</th><th>StdDev</th><th>Median</th><th>Gen0</th><th>Gen1</th><th>Gen2</th><th>Allocated</th>
</tr>
</thead><tbody><tr><td>JsonSerialize</td><td>10000</td><td>5.083 ms</td><td>0.0797 ms</td><td>0.0707 ms</td><td>5.074 ms</td><td>898.4375</td><td>820.3125</td><td>437.5000</td><td>6.24 MB</td>
</tr><tr><td>MpackSerialize</td><td>10000</td><td>1.346 ms</td><td>0.0506 ms</td><td>0.1459 ms</td><td>1.274 ms</td><td>201.1719</td><td>201.1719</td><td>201.1719</td><td>1.58 MB</td>
</tr><tr><td>JsonSerializeAndDeserialize</td><td>10000</td><td>17.712 ms</td><td>0.5170 ms</td><td>1.5082 ms</td><td>17.243 ms</td><td>1187.5000</td><td>1000.0000</td><td>500.0000</td><td>8.33 MB</td>
</tr><tr><td>MpackSerializeAndDeserialize</td><td>10000</td><td>2.907 ms</td><td>0.1571 ms</td><td>0.4533 ms</td><td>2.787 ms</td><td>324.2188</td><td>289.0625</td><td>257.8125</td><td>2.12 MB</td>
</tr><tr><td>MpackSerializeAndDeserializeV2</td><td>10000</td><td>NA</td><td>NA</td><td>NA</td><td>NA</td><td>NA</td><td>NA</td><td>NA</td><td>NA</td>
</tr><tr><td>JsonSerialize</td><td>100000</td><td>69.560 ms</td><td>2.0161 ms</td><td>5.6864 ms</td><td>69.169 ms</td><td>6000.0000</td><td>3875.0000</td><td>1500.0000</td><td>62.3 MB</td>
</tr><tr><td>MpackSerialize</td><td>100000</td><td>11.776 ms</td><td>0.2327 ms</td><td>0.3338 ms</td><td>11.808 ms</td><td>484.3750</td><td>484.3750</td><td>484.3750</td><td>15.83 MB</td>
</tr><tr><td>JsonSerializeAndDeserialize</td><td>100000</td><td>178.753 ms</td><td>3.5128 ms</td><td>6.5979 ms</td><td>177.648 ms</td><td>8000.0000</td><td>4250.0000</td><td>1250.0000</td><td>82.62 MB</td>
</tr><tr><td>MpackSerializeAndDeserialize</td><td>100000</td><td>26.440 ms</td><td>0.6105 ms</td><td>1.7808 ms</td><td>26.394 ms</td><td>1468.7500</td><td>1406.2500</td><td>906.2500</td><td>21.17 MB</td>
</tr><tr><td>MpackSerializeAndDeserializeV2</td><td>100000</td><td>NA</td><td>NA</td><td>NA</td><td>NA</td><td>NA</td><td>NA</td><td>NA</td><td>NA</td>
</tr></tbody></table>
</body>
</html>
