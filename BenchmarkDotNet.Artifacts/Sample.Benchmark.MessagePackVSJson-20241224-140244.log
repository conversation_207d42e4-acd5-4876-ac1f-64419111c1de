// Validating benchmarks:
// ***** BenchmarkRunner: Start   *****
// ***** Found 8 benchmark(s) in total *****
// ***** Building 1 exe(s) in Parallel: Start   *****
// start dotnet  restore /p:UseSharedCompilation=false /p:BuildInParallel=false /m:1 /p:Deterministic=true /p:Optimize=true /p:IntermediateOutputPath="D:\代码\Sample.Benchmark\bin\Release\net9.0\76cffd50-363e-4646-bc48-028b557b8652\obj\Release\net9.0/" /p:OutDir="D:\代码\Sample.Benchmark\bin\Release\net9.0\76cffd50-363e-4646-bc48-028b557b8652\bin\Release\net9.0/" /p:OutputPath="D:\代码\Sample.Benchmark\bin\Release\net9.0\76cffd50-363e-4646-bc48-028b557b8652\bin\Release\net9.0/" in D:\代码\Sample.Benchmark\bin\Release\net9.0\76cffd50-363e-4646-bc48-028b557b8652
// command took 3.36 sec and exited with 0
// start dotnet  build -c Release --no-restore /p:UseSharedCompilation=false /p:BuildInParallel=false /m:1 /p:Deterministic=true /p:Optimize=true /p:IntermediateOutputPath="D:\代码\Sample.Benchmark\bin\Release\net9.0\76cffd50-363e-4646-bc48-028b557b8652\obj\Release\net9.0/" /p:OutDir="D:\代码\Sample.Benchmark\bin\Release\net9.0\76cffd50-363e-4646-bc48-028b557b8652\bin\Release\net9.0/" /p:OutputPath="D:\代码\Sample.Benchmark\bin\Release\net9.0\76cffd50-363e-4646-bc48-028b557b8652\bin\Release\net9.0/" --output "D:\代码\Sample.Benchmark\bin\Release\net9.0\76cffd50-363e-4646-bc48-028b557b8652\bin\Release\net9.0/" in D:\代码\Sample.Benchmark\bin\Release\net9.0\76cffd50-363e-4646-bc48-028b557b8652
// command took 12.26 sec and exited with 0
// ***** Done, took 00:00:15 (15.71 sec)   *****
// Found 8 benchmarks:
//   MessagePackVSJson.JsonSerialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.MpackSerialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.JsonSerialize: DefaultJob [Amount=100000]
//   MessagePackVSJson.MpackSerialize: DefaultJob [Amount=100000]
//   MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=100000]
//   MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=100000]

Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.JsonSerialize: DefaultJob [Amount=10000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 76cffd50-363e-4646-bc48-028b557b8652.dll --anonymousPipes 1400 1408 --benchmarkName "Sample.Benchmark.MessagePackVSJson.JsonSerialize(Amount: 10000)" --job Default --benchmarkId 0 in D:\代码\Sample.Benchmark\bin\Release\net9.0\76cffd50-363e-4646-bc48-028b557b8652\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 223900.00 ns, 223.9000 us/op
WorkloadJitting  1: 1 op, 104077100.00 ns, 104.0771 ms/op

WorkloadPilot    1: 2 op, 67407400.00 ns, 33.7037 ms/op
WorkloadPilot    2: 3 op, 90398800.00 ns, 30.1329 ms/op
WorkloadPilot    3: 4 op, 124448900.00 ns, 31.1122 ms/op
WorkloadPilot    4: 5 op, 50280000.00 ns, 10.0560 ms/op
WorkloadPilot    5: 6 op, 35440000.00 ns, 5.9067 ms/op
WorkloadPilot    6: 7 op, 41555700.00 ns, 5.9365 ms/op
WorkloadPilot    7: 8 op, 46898600.00 ns, 5.8623 ms/op
WorkloadPilot    8: 9 op, 58081400.00 ns, 6.4535 ms/op
WorkloadPilot    9: 10 op, 57470000.00 ns, 5.7470 ms/op
WorkloadPilot   10: 11 op, 60984400.00 ns, 5.5440 ms/op
WorkloadPilot   11: 12 op, 84130300.00 ns, 7.0109 ms/op
WorkloadPilot   12: 13 op, 75175600.00 ns, 5.7827 ms/op
WorkloadPilot   13: 14 op, 78286600.00 ns, 5.5919 ms/op
WorkloadPilot   14: 15 op, 84460900.00 ns, 5.6307 ms/op
WorkloadPilot   15: 16 op, 90514200.00 ns, 5.6571 ms/op
WorkloadPilot   16: 32 op, 181421200.00 ns, 5.6694 ms/op
WorkloadPilot   17: 64 op, 341630500.00 ns, 5.3380 ms/op
WorkloadPilot   18: 128 op, 679171600.00 ns, 5.3060 ms/op

WorkloadWarmup   1: 128 op, 668995100.00 ns, 5.2265 ms/op
WorkloadWarmup   2: 128 op, 676458200.00 ns, 5.2848 ms/op
WorkloadWarmup   3: 128 op, 680436600.00 ns, 5.3159 ms/op
WorkloadWarmup   4: 128 op, 689182700.00 ns, 5.3842 ms/op
WorkloadWarmup   5: 128 op, 686026000.00 ns, 5.3596 ms/op
WorkloadWarmup   6: 128 op, 674154900.00 ns, 5.2668 ms/op
WorkloadWarmup   7: 128 op, 658740100.00 ns, 5.1464 ms/op
WorkloadWarmup   8: 128 op, 648065700.00 ns, 5.0630 ms/op
WorkloadWarmup   9: 128 op, 803762600.00 ns, 6.2794 ms/op
WorkloadWarmup  10: 128 op, 831292600.00 ns, 6.4945 ms/op
WorkloadWarmup  11: 128 op, 788656200.00 ns, 6.1614 ms/op

// BeforeActualRun
WorkloadActual   1: 128 op, 692206200.00 ns, 5.4079 ms/op
WorkloadActual   2: 128 op, 725549500.00 ns, 5.6684 ms/op
WorkloadActual   3: 128 op, 649972900.00 ns, 5.0779 ms/op
WorkloadActual   4: 128 op, 657878100.00 ns, 5.1397 ms/op
WorkloadActual   5: 128 op, 651979400.00 ns, 5.0936 ms/op
WorkloadActual   6: 128 op, 646891500.00 ns, 5.0538 ms/op
WorkloadActual   7: 128 op, 665257700.00 ns, 5.1973 ms/op
WorkloadActual   8: 128 op, 760055200.00 ns, 5.9379 ms/op
WorkloadActual   9: 128 op, 648459000.00 ns, 5.0661 ms/op
WorkloadActual  10: 128 op, 710320100.00 ns, 5.5494 ms/op
WorkloadActual  11: 128 op, 714354800.00 ns, 5.5809 ms/op
WorkloadActual  12: 128 op, 650490500.00 ns, 5.0820 ms/op
WorkloadActual  13: 128 op, 668996600.00 ns, 5.2265 ms/op
WorkloadActual  14: 128 op, 650030400.00 ns, 5.0784 ms/op
WorkloadActual  15: 128 op, 677113300.00 ns, 5.2899 ms/op
WorkloadActual  16: 128 op, 726052700.00 ns, 5.6723 ms/op
WorkloadActual  17: 128 op, 772301900.00 ns, 6.0336 ms/op
WorkloadActual  18: 128 op, 737378700.00 ns, 5.7608 ms/op
WorkloadActual  19: 128 op, 754720100.00 ns, 5.8963 ms/op
WorkloadActual  20: 128 op, 706720600.00 ns, 5.5213 ms/op
WorkloadActual  21: 128 op, 674379400.00 ns, 5.2686 ms/op
WorkloadActual  22: 128 op, 673334600.00 ns, 5.2604 ms/op
WorkloadActual  23: 128 op, 663138400.00 ns, 5.1808 ms/op
WorkloadActual  24: 128 op, 667487400.00 ns, 5.2147 ms/op
WorkloadActual  25: 128 op, 656874400.00 ns, 5.1318 ms/op
WorkloadActual  26: 128 op, 647247300.00 ns, 5.0566 ms/op
WorkloadActual  27: 128 op, 654767500.00 ns, 5.1154 ms/op
WorkloadActual  28: 128 op, 658917300.00 ns, 5.1478 ms/op
WorkloadActual  29: 128 op, 808321200.00 ns, 6.3150 ms/op
WorkloadActual  30: 128 op, 716754200.00 ns, 5.5996 ms/op
WorkloadActual  31: 128 op, 735636100.00 ns, 5.7472 ms/op
WorkloadActual  32: 128 op, 691587700.00 ns, 5.4030 ms/op
WorkloadActual  33: 128 op, 678403100.00 ns, 5.3000 ms/op
WorkloadActual  34: 128 op, 664163000.00 ns, 5.1888 ms/op
WorkloadActual  35: 128 op, 653064100.00 ns, 5.1021 ms/op
WorkloadActual  36: 128 op, 712635500.00 ns, 5.5675 ms/op
WorkloadActual  37: 128 op, 673768900.00 ns, 5.2638 ms/op
