// Validating benchmarks:
// ***** BenchmarkRunner: Start   *****
// ***** Found 10 benchmark(s) in total *****
// ***** Building 1 exe(s) in Parallel: Start   *****
// start dotnet  restore /p:UseSharedCompilation=false /p:BuildInParallel=false /m:1 /p:Deterministic=true /p:Optimize=true /p:IntermediateOutputPath="D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\obj\Release\net9.0/" /p:OutDir="D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0/" /p:OutputPath="D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0/" in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1
// command took 1.37 sec and exited with 0
// start dotnet  build -c Release --no-restore /p:UseSharedCompilation=false /p:BuildInParallel=false /m:1 /p:Deterministic=true /p:Optimize=true /p:IntermediateOutputPath="D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\obj\Release\net9.0/" /p:OutDir="D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0/" /p:OutputPath="D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0/" --output "D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0/" in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1
// command took 5.9 sec and exited with 0
// ***** Done, took 00:00:07 (7.34 sec)   *****
// Found 10 benchmarks:
//   MessagePackVSJson.JsonSerialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.MpackSerialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.MpackSerializeAndDeserializeV2: DefaultJob [Amount=10000]
//   MessagePackVSJson.JsonSerialize: DefaultJob [Amount=100000]
//   MessagePackVSJson.MpackSerialize: DefaultJob [Amount=100000]
//   MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=100000]
//   MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=100000]
//   MessagePackVSJson.MpackSerializeAndDeserializeV2: DefaultJob [Amount=100000]

Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.JsonSerialize: DefaultJob [Amount=10000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 875a7855-6805-431f-81e6-09e019e75bd1.dll --anonymousPipes 1412 1416 --benchmarkName "Sample.Benchmark.MessagePackVSJson.JsonSerialize(Amount: 10000)" --job Default --benchmarkId 0 in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 183800.00 ns, 183.8000 us/op
WorkloadJitting  1: 1 op, 92571900.00 ns, 92.5719 ms/op

WorkloadPilot    1: 2 op, 55406600.00 ns, 27.7033 ms/op
WorkloadPilot    2: 3 op, 77853400.00 ns, 25.9511 ms/op
WorkloadPilot    3: 4 op, 112883000.00 ns, 28.2208 ms/op
WorkloadPilot    4: 5 op, 37977000.00 ns, 7.5954 ms/op
WorkloadPilot    5: 6 op, 33873300.00 ns, 5.6456 ms/op
WorkloadPilot    6: 7 op, 38192500.00 ns, 5.4561 ms/op
WorkloadPilot    7: 8 op, 43254800.00 ns, 5.4069 ms/op
WorkloadPilot    8: 9 op, 56239800.00 ns, 6.2489 ms/op
WorkloadPilot    9: 10 op, 52928400.00 ns, 5.2928 ms/op
WorkloadPilot   10: 11 op, 62103400.00 ns, 5.6458 ms/op
WorkloadPilot   11: 12 op, 64940700.00 ns, 5.4117 ms/op
WorkloadPilot   12: 13 op, 69294700.00 ns, 5.3304 ms/op
WorkloadPilot   13: 14 op, 76900000.00 ns, 5.4929 ms/op
WorkloadPilot   14: 15 op, 84351300.00 ns, 5.6234 ms/op
WorkloadPilot   15: 16 op, 84589000.00 ns, 5.2868 ms/op
WorkloadPilot   16: 32 op, 174867500.00 ns, 5.4646 ms/op
WorkloadPilot   17: 64 op, 360339700.00 ns, 5.6303 ms/op
WorkloadPilot   18: 128 op, 802265400.00 ns, 6.2677 ms/op

WorkloadWarmup   1: 128 op, 785347900.00 ns, 6.1355 ms/op
WorkloadWarmup   2: 128 op, 847975900.00 ns, 6.6248 ms/op
WorkloadWarmup   3: 128 op, 663529000.00 ns, 5.1838 ms/op
WorkloadWarmup   4: 128 op, 654937800.00 ns, 5.1167 ms/op
WorkloadWarmup   5: 128 op, 663692200.00 ns, 5.1851 ms/op
WorkloadWarmup   6: 128 op, 642681300.00 ns, 5.0209 ms/op

// BeforeActualRun
WorkloadActual   1: 128 op, 660738900.00 ns, 5.1620 ms/op
WorkloadActual   2: 128 op, 641495600.00 ns, 5.0117 ms/op
WorkloadActual   3: 128 op, 662907200.00 ns, 5.1790 ms/op
WorkloadActual   4: 128 op, 645636300.00 ns, 5.0440 ms/op
WorkloadActual   5: 128 op, 651498700.00 ns, 5.0898 ms/op
WorkloadActual   6: 128 op, 645771000.00 ns, 5.0451 ms/op
WorkloadActual   7: 128 op, 653900900.00 ns, 5.1086 ms/op
WorkloadActual   8: 128 op, 653647900.00 ns, 5.1066 ms/op
WorkloadActual   9: 128 op, 640034200.00 ns, 5.0003 ms/op
WorkloadActual  10: 128 op, 637251200.00 ns, 4.9785 ms/op
WorkloadActual  11: 128 op, 668891800.00 ns, 5.2257 ms/op
WorkloadActual  12: 128 op, 647329000.00 ns, 5.0573 ms/op
WorkloadActual  13: 128 op, 653026900.00 ns, 5.1018 ms/op
WorkloadActual  14: 128 op, 702235100.00 ns, 5.4862 ms/op
WorkloadActual  15: 128 op, 646550000.00 ns, 5.0512 ms/op

// AfterActualRun
WorkloadResult   1: 128 op, 660738900.00 ns, 5.1620 ms/op
WorkloadResult   2: 128 op, 641495600.00 ns, 5.0117 ms/op
WorkloadResult   3: 128 op, 662907200.00 ns, 5.1790 ms/op
WorkloadResult   4: 128 op, 645636300.00 ns, 5.0440 ms/op
WorkloadResult   5: 128 op, 651498700.00 ns, 5.0898 ms/op
WorkloadResult   6: 128 op, 645771000.00 ns, 5.0451 ms/op
WorkloadResult   7: 128 op, 653900900.00 ns, 5.1086 ms/op
WorkloadResult   8: 128 op, 653647900.00 ns, 5.1066 ms/op
WorkloadResult   9: 128 op, 640034200.00 ns, 5.0003 ms/op
WorkloadResult  10: 128 op, 637251200.00 ns, 4.9785 ms/op
WorkloadResult  11: 128 op, 668891800.00 ns, 5.2257 ms/op
WorkloadResult  12: 128 op, 647329000.00 ns, 5.0573 ms/op
WorkloadResult  13: 128 op, 653026900.00 ns, 5.1018 ms/op
WorkloadResult  14: 128 op, 646550000.00 ns, 5.0512 ms/op
// GC:  115 105 56 837938976 128
// Threading:  0 0 128

// AfterAll
// Benchmark Process 4360 has exited with code 0.

Mean = 5.083 ms, StdErr = 0.019 ms (0.37%), N = 14, StdDev = 0.071 ms
Min = 4.979 ms, Q1 = 5.044 ms, Median = 5.074 ms, Q3 = 5.108 ms, Max = 5.226 ms
IQR = 0.064 ms, LowerFence = 4.949 ms, UpperFence = 5.204 ms
ConfidenceInterval = [5.003 ms; 5.163 ms] (CI 99.9%), Margin = 0.080 ms (1.57% of Mean)
Skewness = 0.42, Kurtosis = 2.11, MValue = 2

// ** Remained 9 (90.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:50 (0h 2m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.MpackSerialize: DefaultJob [Amount=10000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 875a7855-6805-431f-81e6-09e019e75bd1.dll --anonymousPipes 1460 1384 --benchmarkName "Sample.Benchmark.MessagePackVSJson.MpackSerialize(Amount: 10000)" --job Default --benchmarkId 1 in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 182700.00 ns, 182.7000 us/op
WorkloadJitting  1: 1 op, 108596700.00 ns, 108.5967 ms/op

WorkloadPilot    1: 2 op, 7185000.00 ns, 3.5925 ms/op
WorkloadPilot    2: 3 op, 10406100.00 ns, 3.4687 ms/op
WorkloadPilot    3: 4 op, 14621600.00 ns, 3.6554 ms/op
WorkloadPilot    4: 5 op, 17005800.00 ns, 3.4012 ms/op
WorkloadPilot    5: 6 op, 22533800.00 ns, 3.7556 ms/op
WorkloadPilot    6: 7 op, 24425300.00 ns, 3.4893 ms/op
WorkloadPilot    7: 8 op, 27600800.00 ns, 3.4501 ms/op
WorkloadPilot    8: 9 op, 26474700.00 ns, 2.9416 ms/op
WorkloadPilot    9: 10 op, 14964700.00 ns, 1.4965 ms/op
WorkloadPilot   10: 11 op, 14715400.00 ns, 1.3378 ms/op
WorkloadPilot   11: 12 op, 16533200.00 ns, 1.3778 ms/op
WorkloadPilot   12: 13 op, 19017500.00 ns, 1.4629 ms/op
WorkloadPilot   13: 14 op, 18060100.00 ns, 1.2900 ms/op
WorkloadPilot   14: 15 op, 19420300.00 ns, 1.2947 ms/op
WorkloadPilot   15: 16 op, 21514300.00 ns, 1.3446 ms/op
WorkloadPilot   16: 32 op, 40276700.00 ns, 1.2586 ms/op
WorkloadPilot   17: 64 op, 84156300.00 ns, 1.3149 ms/op
WorkloadPilot   18: 128 op, 159043200.00 ns, 1.2425 ms/op
WorkloadPilot   19: 256 op, 322003800.00 ns, 1.2578 ms/op
WorkloadPilot   20: 512 op, 613805700.00 ns, 1.1988 ms/op

WorkloadWarmup   1: 512 op, 627630600.00 ns, 1.2258 ms/op
WorkloadWarmup   2: 512 op, 783633500.00 ns, 1.5305 ms/op
WorkloadWarmup   3: 512 op, 653550400.00 ns, 1.2765 ms/op
WorkloadWarmup   4: 512 op, 688768300.00 ns, 1.3453 ms/op
WorkloadWarmup   5: 512 op, 677488200.00 ns, 1.3232 ms/op
WorkloadWarmup   6: 512 op, 830913800.00 ns, 1.6229 ms/op

// BeforeActualRun
WorkloadActual   1: 512 op, 824449800.00 ns, 1.6103 ms/op
WorkloadActual   2: 512 op, 914047900.00 ns, 1.7852 ms/op
WorkloadActual   3: 512 op, 778468300.00 ns, 1.5204 ms/op
WorkloadActual   4: 512 op, 770265900.00 ns, 1.5044 ms/op
WorkloadActual   5: 512 op, 789471400.00 ns, 1.5419 ms/op
WorkloadActual   6: 512 op, 769765300.00 ns, 1.5034 ms/op
WorkloadActual   7: 512 op, 774490100.00 ns, 1.5127 ms/op
WorkloadActual   8: 512 op, 830135700.00 ns, 1.6214 ms/op
WorkloadActual   9: 512 op, 831614900.00 ns, 1.6242 ms/op
WorkloadActual  10: 512 op, 734564200.00 ns, 1.4347 ms/op
WorkloadActual  11: 512 op, 738322000.00 ns, 1.4420 ms/op
WorkloadActual  12: 512 op, 717763400.00 ns, 1.4019 ms/op
WorkloadActual  13: 512 op, 724759200.00 ns, 1.4155 ms/op
WorkloadActual  14: 512 op, 820065300.00 ns, 1.6017 ms/op
WorkloadActual  15: 512 op, 912932900.00 ns, 1.7831 ms/op
WorkloadActual  16: 512 op, 859023200.00 ns, 1.6778 ms/op
WorkloadActual  17: 512 op, 961252300.00 ns, 1.8774 ms/op
WorkloadActual  18: 512 op, 940187200.00 ns, 1.8363 ms/op
WorkloadActual  19: 512 op, 903061900.00 ns, 1.7638 ms/op
WorkloadActual  20: 512 op, 903907800.00 ns, 1.7654 ms/op
WorkloadActual  21: 512 op, 818367500.00 ns, 1.5984 ms/op
WorkloadActual  22: 512 op, 743803700.00 ns, 1.4527 ms/op
WorkloadActual  23: 512 op, 795115000.00 ns, 1.5530 ms/op
WorkloadActual  24: 512 op, 820026500.00 ns, 1.6016 ms/op
WorkloadActual  25: 512 op, 693110300.00 ns, 1.3537 ms/op
WorkloadActual  26: 512 op, 681577500.00 ns, 1.3312 ms/op
WorkloadActual  27: 512 op, 667566600.00 ns, 1.3038 ms/op
WorkloadActual  28: 512 op, 761792800.00 ns, 1.4879 ms/op
WorkloadActual  29: 512 op, 841255800.00 ns, 1.6431 ms/op
WorkloadActual  30: 512 op, 812738900.00 ns, 1.5874 ms/op
WorkloadActual  31: 512 op, 742918600.00 ns, 1.4510 ms/op
WorkloadActual  32: 512 op, 700956000.00 ns, 1.3691 ms/op
WorkloadActual  33: 512 op, 694035900.00 ns, 1.3555 ms/op
WorkloadActual  34: 512 op, 782421000.00 ns, 1.5282 ms/op
WorkloadActual  35: 512 op, 699498100.00 ns, 1.3662 ms/op
WorkloadActual  36: 512 op, 639676700.00 ns, 1.2494 ms/op
WorkloadActual  37: 512 op, 647625200.00 ns, 1.2649 ms/op
WorkloadActual  38: 512 op, 654335100.00 ns, 1.2780 ms/op
WorkloadActual  39: 512 op, 615181000.00 ns, 1.2015 ms/op
WorkloadActual  40: 512 op, 638704800.00 ns, 1.2475 ms/op
WorkloadActual  41: 512 op, 694770800.00 ns, 1.3570 ms/op
WorkloadActual  42: 512 op, 658880500.00 ns, 1.2869 ms/op
WorkloadActual  43: 512 op, 645411300.00 ns, 1.2606 ms/op
WorkloadActual  44: 512 op, 661350900.00 ns, 1.2917 ms/op
WorkloadActual  45: 512 op, 639008500.00 ns, 1.2481 ms/op
WorkloadActual  46: 512 op, 651282000.00 ns, 1.2720 ms/op
WorkloadActual  47: 512 op, 636559400.00 ns, 1.2433 ms/op
WorkloadActual  48: 512 op, 651732700.00 ns, 1.2729 ms/op
WorkloadActual  49: 512 op, 647327000.00 ns, 1.2643 ms/op
WorkloadActual  50: 512 op, 643591300.00 ns, 1.2570 ms/op
WorkloadActual  51: 512 op, 633047700.00 ns, 1.2364 ms/op
WorkloadActual  52: 512 op, 656845400.00 ns, 1.2829 ms/op
WorkloadActual  53: 512 op, 639179500.00 ns, 1.2484 ms/op
WorkloadActual  54: 512 op, 639769200.00 ns, 1.2495 ms/op
WorkloadActual  55: 512 op, 643002800.00 ns, 1.2559 ms/op
WorkloadActual  56: 512 op, 645249800.00 ns, 1.2603 ms/op
WorkloadActual  57: 512 op, 647768200.00 ns, 1.2652 ms/op
WorkloadActual  58: 512 op, 633939300.00 ns, 1.2382 ms/op
WorkloadActual  59: 512 op, 653305300.00 ns, 1.2760 ms/op
WorkloadActual  60: 512 op, 645275800.00 ns, 1.2603 ms/op
WorkloadActual  61: 512 op, 657147000.00 ns, 1.2835 ms/op
WorkloadActual  62: 512 op, 619459200.00 ns, 1.2099 ms/op
WorkloadActual  63: 512 op, 635474400.00 ns, 1.2412 ms/op
WorkloadActual  64: 512 op, 628608100.00 ns, 1.2278 ms/op
WorkloadActual  65: 512 op, 633974000.00 ns, 1.2382 ms/op
WorkloadActual  66: 512 op, 631159400.00 ns, 1.2327 ms/op
WorkloadActual  67: 512 op, 620320600.00 ns, 1.2116 ms/op
WorkloadActual  68: 512 op, 624084200.00 ns, 1.2189 ms/op
WorkloadActual  69: 512 op, 619216100.00 ns, 1.2094 ms/op
WorkloadActual  70: 512 op, 607529300.00 ns, 1.1866 ms/op
WorkloadActual  71: 512 op, 608272100.00 ns, 1.1880 ms/op
WorkloadActual  72: 512 op, 723444700.00 ns, 1.4130 ms/op
WorkloadActual  73: 512 op, 724261500.00 ns, 1.4146 ms/op
WorkloadActual  74: 512 op, 627348900.00 ns, 1.2253 ms/op
WorkloadActual  75: 512 op, 635424900.00 ns, 1.2411 ms/op
WorkloadActual  76: 512 op, 607656800.00 ns, 1.1868 ms/op
WorkloadActual  77: 512 op, 630588300.00 ns, 1.2316 ms/op
WorkloadActual  78: 512 op, 618364700.00 ns, 1.2077 ms/op
WorkloadActual  79: 512 op, 625942600.00 ns, 1.2225 ms/op
WorkloadActual  80: 512 op, 614559500.00 ns, 1.2003 ms/op
WorkloadActual  81: 512 op, 667002100.00 ns, 1.3027 ms/op
WorkloadActual  82: 512 op, 637091400.00 ns, 1.2443 ms/op
WorkloadActual  83: 512 op, 632801200.00 ns, 1.2359 ms/op
WorkloadActual  84: 512 op, 611626300.00 ns, 1.1946 ms/op
WorkloadActual  85: 512 op, 640201000.00 ns, 1.2504 ms/op
WorkloadActual  86: 512 op, 630096900.00 ns, 1.2307 ms/op
WorkloadActual  87: 512 op, 627160900.00 ns, 1.2249 ms/op
WorkloadActual  88: 512 op, 615002600.00 ns, 1.2012 ms/op
WorkloadActual  89: 512 op, 627517600.00 ns, 1.2256 ms/op
WorkloadActual  90: 512 op, 718730800.00 ns, 1.4038 ms/op
WorkloadActual  91: 512 op, 674385600.00 ns, 1.3172 ms/op
WorkloadActual  92: 512 op, 640824000.00 ns, 1.2516 ms/op
WorkloadActual  93: 512 op, 726944600.00 ns, 1.4198 ms/op
WorkloadActual  94: 512 op, 623466500.00 ns, 1.2177 ms/op
WorkloadActual  95: 512 op, 726134300.00 ns, 1.4182 ms/op
WorkloadActual  96: 512 op, 765030800.00 ns, 1.4942 ms/op
WorkloadActual  97: 512 op, 735596700.00 ns, 1.4367 ms/op
WorkloadActual  98: 512 op, 642447000.00 ns, 1.2548 ms/op
WorkloadActual  99: 512 op, 674025600.00 ns, 1.3165 ms/op
WorkloadActual  100: 512 op, 639954300.00 ns, 1.2499 ms/op

// AfterActualRun
WorkloadResult   1: 512 op, 824449800.00 ns, 1.6103 ms/op
WorkloadResult   2: 512 op, 778468300.00 ns, 1.5204 ms/op
WorkloadResult   3: 512 op, 770265900.00 ns, 1.5044 ms/op
WorkloadResult   4: 512 op, 789471400.00 ns, 1.5419 ms/op
WorkloadResult   5: 512 op, 769765300.00 ns, 1.5034 ms/op
WorkloadResult   6: 512 op, 774490100.00 ns, 1.5127 ms/op
WorkloadResult   7: 512 op, 830135700.00 ns, 1.6214 ms/op
WorkloadResult   8: 512 op, 831614900.00 ns, 1.6242 ms/op
WorkloadResult   9: 512 op, 734564200.00 ns, 1.4347 ms/op
WorkloadResult  10: 512 op, 738322000.00 ns, 1.4420 ms/op
WorkloadResult  11: 512 op, 717763400.00 ns, 1.4019 ms/op
WorkloadResult  12: 512 op, 724759200.00 ns, 1.4155 ms/op
WorkloadResult  13: 512 op, 820065300.00 ns, 1.6017 ms/op
WorkloadResult  14: 512 op, 859023200.00 ns, 1.6778 ms/op
WorkloadResult  15: 512 op, 903061900.00 ns, 1.7638 ms/op
WorkloadResult  16: 512 op, 903907800.00 ns, 1.7654 ms/op
WorkloadResult  17: 512 op, 818367500.00 ns, 1.5984 ms/op
WorkloadResult  18: 512 op, 743803700.00 ns, 1.4527 ms/op
WorkloadResult  19: 512 op, 795115000.00 ns, 1.5530 ms/op
WorkloadResult  20: 512 op, 820026500.00 ns, 1.6016 ms/op
WorkloadResult  21: 512 op, 693110300.00 ns, 1.3537 ms/op
WorkloadResult  22: 512 op, 681577500.00 ns, 1.3312 ms/op
WorkloadResult  23: 512 op, 667566600.00 ns, 1.3038 ms/op
WorkloadResult  24: 512 op, 761792800.00 ns, 1.4879 ms/op
WorkloadResult  25: 512 op, 841255800.00 ns, 1.6431 ms/op
WorkloadResult  26: 512 op, 812738900.00 ns, 1.5874 ms/op
WorkloadResult  27: 512 op, 742918600.00 ns, 1.4510 ms/op
WorkloadResult  28: 512 op, 700956000.00 ns, 1.3691 ms/op
WorkloadResult  29: 512 op, 694035900.00 ns, 1.3555 ms/op
WorkloadResult  30: 512 op, 782421000.00 ns, 1.5282 ms/op
WorkloadResult  31: 512 op, 699498100.00 ns, 1.3662 ms/op
WorkloadResult  32: 512 op, 639676700.00 ns, 1.2494 ms/op
WorkloadResult  33: 512 op, 647625200.00 ns, 1.2649 ms/op
WorkloadResult  34: 512 op, 654335100.00 ns, 1.2780 ms/op
WorkloadResult  35: 512 op, 615181000.00 ns, 1.2015 ms/op
WorkloadResult  36: 512 op, 638704800.00 ns, 1.2475 ms/op
WorkloadResult  37: 512 op, 694770800.00 ns, 1.3570 ms/op
WorkloadResult  38: 512 op, 658880500.00 ns, 1.2869 ms/op
WorkloadResult  39: 512 op, 645411300.00 ns, 1.2606 ms/op
WorkloadResult  40: 512 op, 661350900.00 ns, 1.2917 ms/op
WorkloadResult  41: 512 op, 639008500.00 ns, 1.2481 ms/op
WorkloadResult  42: 512 op, 651282000.00 ns, 1.2720 ms/op
WorkloadResult  43: 512 op, 636559400.00 ns, 1.2433 ms/op
WorkloadResult  44: 512 op, 651732700.00 ns, 1.2729 ms/op
WorkloadResult  45: 512 op, 647327000.00 ns, 1.2643 ms/op
WorkloadResult  46: 512 op, 643591300.00 ns, 1.2570 ms/op
WorkloadResult  47: 512 op, 633047700.00 ns, 1.2364 ms/op
WorkloadResult  48: 512 op, 656845400.00 ns, 1.2829 ms/op
WorkloadResult  49: 512 op, 639179500.00 ns, 1.2484 ms/op
WorkloadResult  50: 512 op, 639769200.00 ns, 1.2495 ms/op
WorkloadResult  51: 512 op, 643002800.00 ns, 1.2559 ms/op
WorkloadResult  52: 512 op, 645249800.00 ns, 1.2603 ms/op
WorkloadResult  53: 512 op, 647768200.00 ns, 1.2652 ms/op
WorkloadResult  54: 512 op, 633939300.00 ns, 1.2382 ms/op
WorkloadResult  55: 512 op, 653305300.00 ns, 1.2760 ms/op
WorkloadResult  56: 512 op, 645275800.00 ns, 1.2603 ms/op
WorkloadResult  57: 512 op, 657147000.00 ns, 1.2835 ms/op
WorkloadResult  58: 512 op, 619459200.00 ns, 1.2099 ms/op
WorkloadResult  59: 512 op, 635474400.00 ns, 1.2412 ms/op
WorkloadResult  60: 512 op, 628608100.00 ns, 1.2278 ms/op
WorkloadResult  61: 512 op, 633974000.00 ns, 1.2382 ms/op
WorkloadResult  62: 512 op, 631159400.00 ns, 1.2327 ms/op
WorkloadResult  63: 512 op, 620320600.00 ns, 1.2116 ms/op
WorkloadResult  64: 512 op, 624084200.00 ns, 1.2189 ms/op
WorkloadResult  65: 512 op, 619216100.00 ns, 1.2094 ms/op
WorkloadResult  66: 512 op, 607529300.00 ns, 1.1866 ms/op
WorkloadResult  67: 512 op, 608272100.00 ns, 1.1880 ms/op
WorkloadResult  68: 512 op, 723444700.00 ns, 1.4130 ms/op
WorkloadResult  69: 512 op, 724261500.00 ns, 1.4146 ms/op
WorkloadResult  70: 512 op, 627348900.00 ns, 1.2253 ms/op
WorkloadResult  71: 512 op, 635424900.00 ns, 1.2411 ms/op
WorkloadResult  72: 512 op, 607656800.00 ns, 1.1868 ms/op
WorkloadResult  73: 512 op, 630588300.00 ns, 1.2316 ms/op
WorkloadResult  74: 512 op, 618364700.00 ns, 1.2077 ms/op
WorkloadResult  75: 512 op, 625942600.00 ns, 1.2225 ms/op
WorkloadResult  76: 512 op, 614559500.00 ns, 1.2003 ms/op
WorkloadResult  77: 512 op, 667002100.00 ns, 1.3027 ms/op
WorkloadResult  78: 512 op, 637091400.00 ns, 1.2443 ms/op
WorkloadResult  79: 512 op, 632801200.00 ns, 1.2359 ms/op
WorkloadResult  80: 512 op, 611626300.00 ns, 1.1946 ms/op
WorkloadResult  81: 512 op, 640201000.00 ns, 1.2504 ms/op
WorkloadResult  82: 512 op, 630096900.00 ns, 1.2307 ms/op
WorkloadResult  83: 512 op, 627160900.00 ns, 1.2249 ms/op
WorkloadResult  84: 512 op, 615002600.00 ns, 1.2012 ms/op
WorkloadResult  85: 512 op, 627517600.00 ns, 1.2256 ms/op
WorkloadResult  86: 512 op, 718730800.00 ns, 1.4038 ms/op
WorkloadResult  87: 512 op, 674385600.00 ns, 1.3172 ms/op
WorkloadResult  88: 512 op, 640824000.00 ns, 1.2516 ms/op
WorkloadResult  89: 512 op, 726944600.00 ns, 1.4198 ms/op
WorkloadResult  90: 512 op, 623466500.00 ns, 1.2177 ms/op
WorkloadResult  91: 512 op, 726134300.00 ns, 1.4182 ms/op
WorkloadResult  92: 512 op, 765030800.00 ns, 1.4942 ms/op
WorkloadResult  93: 512 op, 735596700.00 ns, 1.4367 ms/op
WorkloadResult  94: 512 op, 642447000.00 ns, 1.2548 ms/op
WorkloadResult  95: 512 op, 674025600.00 ns, 1.3165 ms/op
WorkloadResult  96: 512 op, 639954300.00 ns, 1.2499 ms/op
// GC:  103 103 103 850254936 512
// Threading:  0 0 512

// AfterAll
// Benchmark Process 6256 has exited with code 0.

Mean = 1.346 ms, StdErr = 0.015 ms (1.11%), N = 96, StdDev = 0.146 ms
Min = 1.187 ms, Q1 = 1.238 ms, Median = 1.274 ms, Q3 = 1.435 ms, Max = 1.765 ms
IQR = 0.197 ms, LowerFence = 0.943 ms, UpperFence = 1.731 ms
ConfidenceInterval = [1.295 ms; 1.396 ms] (CI 99.9%), Margin = 0.051 ms (3.76% of Mean)
Skewness = 1.05, Kurtosis = 3.06, MValue = 2.44

// ** Remained 8 (80.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:55 (0h 6m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=10000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 875a7855-6805-431f-81e6-09e019e75bd1.dll --anonymousPipes 976 972 --benchmarkName "Sample.Benchmark.MessagePackVSJson.JsonSerializeAndDeserialize(Amount: 10000)" --job Default --benchmarkId 2 in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 274700.00 ns, 274.7000 us/op
WorkloadJitting  1: 1 op, 233956000.00 ns, 233.9560 ms/op

WorkloadPilot    1: 2 op, 205264500.00 ns, 102.6323 ms/op
WorkloadPilot    2: 3 op, 193657500.00 ns, 64.5525 ms/op
WorkloadPilot    3: 4 op, 147810000.00 ns, 36.9525 ms/op
WorkloadPilot    4: 5 op, 83628900.00 ns, 16.7258 ms/op
WorkloadPilot    5: 6 op, 103010100.00 ns, 17.1684 ms/op
WorkloadPilot    6: 7 op, 120076700.00 ns, 17.1538 ms/op
WorkloadPilot    7: 8 op, 146836500.00 ns, 18.3546 ms/op
WorkloadPilot    8: 9 op, 171241700.00 ns, 19.0269 ms/op
WorkloadPilot    9: 10 op, 205533600.00 ns, 20.5534 ms/op
WorkloadPilot   10: 11 op, 195272700.00 ns, 17.7521 ms/op
WorkloadPilot   11: 12 op, 196276400.00 ns, 16.3564 ms/op
WorkloadPilot   12: 13 op, 215377300.00 ns, 16.5675 ms/op
WorkloadPilot   13: 14 op, 240664900.00 ns, 17.1904 ms/op
WorkloadPilot   14: 15 op, 245916300.00 ns, 16.3944 ms/op
WorkloadPilot   15: 16 op, 260042400.00 ns, 16.2527 ms/op
WorkloadPilot   16: 32 op, 553712100.00 ns, 17.3035 ms/op

WorkloadWarmup   1: 32 op, 595849900.00 ns, 18.6203 ms/op
WorkloadWarmup   2: 32 op, 544399400.00 ns, 17.0125 ms/op
WorkloadWarmup   3: 32 op, 517180600.00 ns, 16.1619 ms/op
WorkloadWarmup   4: 32 op, 521547200.00 ns, 16.2984 ms/op
WorkloadWarmup   5: 32 op, 537240500.00 ns, 16.7888 ms/op
WorkloadWarmup   6: 32 op, 493680600.00 ns, 15.4275 ms/op
WorkloadWarmup   7: 32 op, 516380700.00 ns, 16.1369 ms/op
WorkloadWarmup   8: 32 op, 516961400.00 ns, 16.1550 ms/op
WorkloadWarmup   9: 32 op, 526767000.00 ns, 16.4615 ms/op
WorkloadWarmup  10: 32 op, 545213300.00 ns, 17.0379 ms/op
WorkloadWarmup  11: 32 op, 691008600.00 ns, 21.5940 ms/op
WorkloadWarmup  12: 32 op, 580456600.00 ns, 18.1393 ms/op

// BeforeActualRun
WorkloadActual   1: 32 op, 659418800.00 ns, 20.6068 ms/op
WorkloadActual   2: 32 op, 643623600.00 ns, 20.1132 ms/op
WorkloadActual   3: 32 op, 606375900.00 ns, 18.9492 ms/op
WorkloadActual   4: 32 op, 542199800.00 ns, 16.9437 ms/op
WorkloadActual   5: 32 op, 543295200.00 ns, 16.9780 ms/op
WorkloadActual   6: 32 op, 577259100.00 ns, 18.0393 ms/op
WorkloadActual   7: 32 op, 584284400.00 ns, 18.2589 ms/op
WorkloadActual   8: 32 op, 548124700.00 ns, 17.1289 ms/op
WorkloadActual   9: 32 op, 648233700.00 ns, 20.2573 ms/op
WorkloadActual  10: 32 op, 652081000.00 ns, 20.3775 ms/op
WorkloadActual  11: 32 op, 673122000.00 ns, 21.0351 ms/op
WorkloadActual  12: 32 op, 684167400.00 ns, 21.3802 ms/op
WorkloadActual  13: 32 op, 711986100.00 ns, 22.2496 ms/op
WorkloadActual  14: 32 op, 642415400.00 ns, 20.0755 ms/op
WorkloadActual  15: 32 op, 676826700.00 ns, 21.1508 ms/op
WorkloadActual  16: 32 op, 702772800.00 ns, 21.9617 ms/op
WorkloadActual  17: 32 op, 683804100.00 ns, 21.3689 ms/op
WorkloadActual  18: 32 op, 654382500.00 ns, 20.4495 ms/op
WorkloadActual  19: 32 op, 569394100.00 ns, 17.7936 ms/op
WorkloadActual  20: 32 op, 548918200.00 ns, 17.1537 ms/op
WorkloadActual  21: 32 op, 550868100.00 ns, 17.2146 ms/op
WorkloadActual  22: 32 op, 541739300.00 ns, 16.9294 ms/op
WorkloadActual  23: 32 op, 529515200.00 ns, 16.5474 ms/op
WorkloadActual  24: 32 op, 676859800.00 ns, 21.1519 ms/op
WorkloadActual  25: 32 op, 588741100.00 ns, 18.3982 ms/op
WorkloadActual  26: 32 op, 564396000.00 ns, 17.6374 ms/op
WorkloadActual  27: 32 op, 607758900.00 ns, 18.9925 ms/op
WorkloadActual  28: 32 op, 562119600.00 ns, 17.5662 ms/op
WorkloadActual  29: 32 op, 535917200.00 ns, 16.7474 ms/op
WorkloadActual  30: 32 op, 527856900.00 ns, 16.4955 ms/op
WorkloadActual  31: 32 op, 542488200.00 ns, 16.9528 ms/op
WorkloadActual  32: 32 op, 529666500.00 ns, 16.5521 ms/op
WorkloadActual  33: 32 op, 546325700.00 ns, 17.0727 ms/op
WorkloadActual  34: 32 op, 587434100.00 ns, 18.3573 ms/op
WorkloadActual  35: 32 op, 559716100.00 ns, 17.4911 ms/op
WorkloadActual  36: 32 op, 554885100.00 ns, 17.3402 ms/op
WorkloadActual  37: 32 op, 535948400.00 ns, 16.7484 ms/op
WorkloadActual  38: 32 op, 540091400.00 ns, 16.8779 ms/op
WorkloadActual  39: 32 op, 523269800.00 ns, 16.3522 ms/op
WorkloadActual  40: 32 op, 531443300.00 ns, 16.6076 ms/op
WorkloadActual  41: 32 op, 529740500.00 ns, 16.5544 ms/op
WorkloadActual  42: 32 op, 555437400.00 ns, 17.3574 ms/op
WorkloadActual  43: 32 op, 558547900.00 ns, 17.4546 ms/op
WorkloadActual  44: 32 op, 646634600.00 ns, 20.2073 ms/op
WorkloadActual  45: 32 op, 570818500.00 ns, 17.8381 ms/op
WorkloadActual  46: 32 op, 589159400.00 ns, 18.4112 ms/op
WorkloadActual  47: 32 op, 625044400.00 ns, 19.5326 ms/op
WorkloadActual  48: 32 op, 631871100.00 ns, 19.7460 ms/op
WorkloadActual  49: 32 op, 552708100.00 ns, 17.2721 ms/op
WorkloadActual  50: 32 op, 528947300.00 ns, 16.5296 ms/op
WorkloadActual  51: 32 op, 624371100.00 ns, 19.5116 ms/op
WorkloadActual  52: 32 op, 537198900.00 ns, 16.7875 ms/op
WorkloadActual  53: 32 op, 594003800.00 ns, 18.5626 ms/op
WorkloadActual  54: 32 op, 553707300.00 ns, 17.3034 ms/op
WorkloadActual  55: 32 op, 577777200.00 ns, 18.0555 ms/op
WorkloadActual  56: 32 op, 584324200.00 ns, 18.2601 ms/op
WorkloadActual  57: 32 op, 567274600.00 ns, 17.7273 ms/op
WorkloadActual  58: 32 op, 555291900.00 ns, 17.3529 ms/op
WorkloadActual  59: 32 op, 535579400.00 ns, 16.7369 ms/op
WorkloadActual  60: 32 op, 569452800.00 ns, 17.7954 ms/op
WorkloadActual  61: 32 op, 557230800.00 ns, 17.4135 ms/op
WorkloadActual  62: 32 op, 564325200.00 ns, 17.6352 ms/op
WorkloadActual  63: 32 op, 607291200.00 ns, 18.9779 ms/op
WorkloadActual  64: 32 op, 617836900.00 ns, 19.3074 ms/op
WorkloadActual  65: 32 op, 643592500.00 ns, 20.1123 ms/op
WorkloadActual  66: 32 op, 612383900.00 ns, 19.1370 ms/op
WorkloadActual  67: 32 op, 524840500.00 ns, 16.4013 ms/op
WorkloadActual  68: 32 op, 540635700.00 ns, 16.8949 ms/op
WorkloadActual  69: 32 op, 525798700.00 ns, 16.4312 ms/op
WorkloadActual  70: 32 op, 571226700.00 ns, 17.8508 ms/op
WorkloadActual  71: 32 op, 527782500.00 ns, 16.4932 ms/op
WorkloadActual  72: 32 op, 523897100.00 ns, 16.3718 ms/op
WorkloadActual  73: 32 op, 537101900.00 ns, 16.7844 ms/op
WorkloadActual  74: 32 op, 512947400.00 ns, 16.0296 ms/op
WorkloadActual  75: 32 op, 489609800.00 ns, 15.3003 ms/op
WorkloadActual  76: 32 op, 516804700.00 ns, 16.1501 ms/op
WorkloadActual  77: 32 op, 507961500.00 ns, 15.8738 ms/op
WorkloadActual  78: 32 op, 535081300.00 ns, 16.7213 ms/op
WorkloadActual  79: 32 op, 547077600.00 ns, 17.0962 ms/op
WorkloadActual  80: 32 op, 614560800.00 ns, 19.2050 ms/op
WorkloadActual  81: 32 op, 531238900.00 ns, 16.6012 ms/op
WorkloadActual  82: 32 op, 624382700.00 ns, 19.5120 ms/op
WorkloadActual  83: 32 op, 586415600.00 ns, 18.3255 ms/op
WorkloadActual  84: 32 op, 591511600.00 ns, 18.4847 ms/op
WorkloadActual  85: 32 op, 523852800.00 ns, 16.3704 ms/op
WorkloadActual  86: 32 op, 531936000.00 ns, 16.6230 ms/op
WorkloadActual  87: 32 op, 533383200.00 ns, 16.6682 ms/op
WorkloadActual  88: 32 op, 545900900.00 ns, 17.0594 ms/op
WorkloadActual  89: 32 op, 528880200.00 ns, 16.5275 ms/op
WorkloadActual  90: 32 op, 538644200.00 ns, 16.8326 ms/op
WorkloadActual  91: 32 op, 510928500.00 ns, 15.9665 ms/op
WorkloadActual  92: 32 op, 517428600.00 ns, 16.1696 ms/op
WorkloadActual  93: 32 op, 505513100.00 ns, 15.7973 ms/op
WorkloadActual  94: 32 op, 506473000.00 ns, 15.8273 ms/op
WorkloadActual  95: 32 op, 528043600.00 ns, 16.5014 ms/op
WorkloadActual  96: 32 op, 522505400.00 ns, 16.3283 ms/op
WorkloadActual  97: 32 op, 506458200.00 ns, 15.8268 ms/op
WorkloadActual  98: 32 op, 549325700.00 ns, 17.1664 ms/op
WorkloadActual  99: 32 op, 507162900.00 ns, 15.8488 ms/op
WorkloadActual  100: 32 op, 514657200.00 ns, 16.0830 ms/op

// AfterActualRun
WorkloadResult   1: 32 op, 659418800.00 ns, 20.6068 ms/op
WorkloadResult   2: 32 op, 643623600.00 ns, 20.1132 ms/op
WorkloadResult   3: 32 op, 606375900.00 ns, 18.9492 ms/op
WorkloadResult   4: 32 op, 542199800.00 ns, 16.9437 ms/op
WorkloadResult   5: 32 op, 543295200.00 ns, 16.9780 ms/op
WorkloadResult   6: 32 op, 577259100.00 ns, 18.0393 ms/op
WorkloadResult   7: 32 op, 584284400.00 ns, 18.2589 ms/op
WorkloadResult   8: 32 op, 548124700.00 ns, 17.1289 ms/op
WorkloadResult   9: 32 op, 648233700.00 ns, 20.2573 ms/op
WorkloadResult  10: 32 op, 652081000.00 ns, 20.3775 ms/op
WorkloadResult  11: 32 op, 673122000.00 ns, 21.0351 ms/op
WorkloadResult  12: 32 op, 684167400.00 ns, 21.3802 ms/op
WorkloadResult  13: 32 op, 642415400.00 ns, 20.0755 ms/op
WorkloadResult  14: 32 op, 676826700.00 ns, 21.1508 ms/op
WorkloadResult  15: 32 op, 683804100.00 ns, 21.3689 ms/op
WorkloadResult  16: 32 op, 654382500.00 ns, 20.4495 ms/op
WorkloadResult  17: 32 op, 569394100.00 ns, 17.7936 ms/op
WorkloadResult  18: 32 op, 548918200.00 ns, 17.1537 ms/op
WorkloadResult  19: 32 op, 550868100.00 ns, 17.2146 ms/op
WorkloadResult  20: 32 op, 541739300.00 ns, 16.9294 ms/op
WorkloadResult  21: 32 op, 529515200.00 ns, 16.5474 ms/op
WorkloadResult  22: 32 op, 676859800.00 ns, 21.1519 ms/op
WorkloadResult  23: 32 op, 588741100.00 ns, 18.3982 ms/op
WorkloadResult  24: 32 op, 564396000.00 ns, 17.6374 ms/op
WorkloadResult  25: 32 op, 607758900.00 ns, 18.9925 ms/op
WorkloadResult  26: 32 op, 562119600.00 ns, 17.5662 ms/op
WorkloadResult  27: 32 op, 535917200.00 ns, 16.7474 ms/op
WorkloadResult  28: 32 op, 527856900.00 ns, 16.4955 ms/op
WorkloadResult  29: 32 op, 542488200.00 ns, 16.9528 ms/op
WorkloadResult  30: 32 op, 529666500.00 ns, 16.5521 ms/op
WorkloadResult  31: 32 op, 546325700.00 ns, 17.0727 ms/op
WorkloadResult  32: 32 op, 587434100.00 ns, 18.3573 ms/op
WorkloadResult  33: 32 op, 559716100.00 ns, 17.4911 ms/op
WorkloadResult  34: 32 op, 554885100.00 ns, 17.3402 ms/op
WorkloadResult  35: 32 op, 535948400.00 ns, 16.7484 ms/op
WorkloadResult  36: 32 op, 540091400.00 ns, 16.8779 ms/op
WorkloadResult  37: 32 op, 523269800.00 ns, 16.3522 ms/op
WorkloadResult  38: 32 op, 531443300.00 ns, 16.6076 ms/op
WorkloadResult  39: 32 op, 529740500.00 ns, 16.5544 ms/op
WorkloadResult  40: 32 op, 555437400.00 ns, 17.3574 ms/op
WorkloadResult  41: 32 op, 558547900.00 ns, 17.4546 ms/op
WorkloadResult  42: 32 op, 646634600.00 ns, 20.2073 ms/op
WorkloadResult  43: 32 op, 570818500.00 ns, 17.8381 ms/op
WorkloadResult  44: 32 op, 589159400.00 ns, 18.4112 ms/op
WorkloadResult  45: 32 op, 625044400.00 ns, 19.5326 ms/op
WorkloadResult  46: 32 op, 631871100.00 ns, 19.7460 ms/op
WorkloadResult  47: 32 op, 552708100.00 ns, 17.2721 ms/op
WorkloadResult  48: 32 op, 528947300.00 ns, 16.5296 ms/op
WorkloadResult  49: 32 op, 624371100.00 ns, 19.5116 ms/op
WorkloadResult  50: 32 op, 537198900.00 ns, 16.7875 ms/op
WorkloadResult  51: 32 op, 594003800.00 ns, 18.5626 ms/op
WorkloadResult  52: 32 op, 553707300.00 ns, 17.3034 ms/op
WorkloadResult  53: 32 op, 577777200.00 ns, 18.0555 ms/op
WorkloadResult  54: 32 op, 584324200.00 ns, 18.2601 ms/op
WorkloadResult  55: 32 op, 567274600.00 ns, 17.7273 ms/op
WorkloadResult  56: 32 op, 555291900.00 ns, 17.3529 ms/op
WorkloadResult  57: 32 op, 535579400.00 ns, 16.7369 ms/op
WorkloadResult  58: 32 op, 569452800.00 ns, 17.7954 ms/op
WorkloadResult  59: 32 op, 557230800.00 ns, 17.4135 ms/op
WorkloadResult  60: 32 op, 564325200.00 ns, 17.6352 ms/op
WorkloadResult  61: 32 op, 607291200.00 ns, 18.9779 ms/op
WorkloadResult  62: 32 op, 617836900.00 ns, 19.3074 ms/op
WorkloadResult  63: 32 op, 643592500.00 ns, 20.1123 ms/op
WorkloadResult  64: 32 op, 612383900.00 ns, 19.1370 ms/op
WorkloadResult  65: 32 op, 524840500.00 ns, 16.4013 ms/op
WorkloadResult  66: 32 op, 540635700.00 ns, 16.8949 ms/op
WorkloadResult  67: 32 op, 525798700.00 ns, 16.4312 ms/op
WorkloadResult  68: 32 op, 571226700.00 ns, 17.8508 ms/op
WorkloadResult  69: 32 op, 527782500.00 ns, 16.4932 ms/op
WorkloadResult  70: 32 op, 523897100.00 ns, 16.3718 ms/op
WorkloadResult  71: 32 op, 537101900.00 ns, 16.7844 ms/op
WorkloadResult  72: 32 op, 512947400.00 ns, 16.0296 ms/op
WorkloadResult  73: 32 op, 489609800.00 ns, 15.3003 ms/op
WorkloadResult  74: 32 op, 516804700.00 ns, 16.1501 ms/op
WorkloadResult  75: 32 op, 507961500.00 ns, 15.8738 ms/op
WorkloadResult  76: 32 op, 535081300.00 ns, 16.7213 ms/op
WorkloadResult  77: 32 op, 547077600.00 ns, 17.0962 ms/op
WorkloadResult  78: 32 op, 614560800.00 ns, 19.2050 ms/op
WorkloadResult  79: 32 op, 531238900.00 ns, 16.6012 ms/op
WorkloadResult  80: 32 op, 624382700.00 ns, 19.5120 ms/op
WorkloadResult  81: 32 op, 586415600.00 ns, 18.3255 ms/op
WorkloadResult  82: 32 op, 591511600.00 ns, 18.4847 ms/op
WorkloadResult  83: 32 op, 523852800.00 ns, 16.3704 ms/op
WorkloadResult  84: 32 op, 531936000.00 ns, 16.6230 ms/op
WorkloadResult  85: 32 op, 533383200.00 ns, 16.6682 ms/op
WorkloadResult  86: 32 op, 545900900.00 ns, 17.0594 ms/op
WorkloadResult  87: 32 op, 528880200.00 ns, 16.5275 ms/op
WorkloadResult  88: 32 op, 538644200.00 ns, 16.8326 ms/op
WorkloadResult  89: 32 op, 510928500.00 ns, 15.9665 ms/op
WorkloadResult  90: 32 op, 517428600.00 ns, 16.1696 ms/op
WorkloadResult  91: 32 op, 505513100.00 ns, 15.7973 ms/op
WorkloadResult  92: 32 op, 506473000.00 ns, 15.8273 ms/op
WorkloadResult  93: 32 op, 528043600.00 ns, 16.5014 ms/op
WorkloadResult  94: 32 op, 522505400.00 ns, 16.3283 ms/op
WorkloadResult  95: 32 op, 506458200.00 ns, 15.8268 ms/op
WorkloadResult  96: 32 op, 549325700.00 ns, 17.1664 ms/op
WorkloadResult  97: 32 op, 507162900.00 ns, 15.8488 ms/op
WorkloadResult  98: 32 op, 514657200.00 ns, 16.0830 ms/op
// GC:  38 32 16 279398320 32
// Threading:  0 0 32

// AfterAll
// Benchmark Process 24136 has exited with code 0.

Mean = 17.712 ms, StdErr = 0.152 ms (0.86%), N = 98, StdDev = 1.508 ms
Min = 15.300 ms, Q1 = 16.566 ms, Median = 17.243 ms, Q3 = 18.466 ms, Max = 21.380 ms
IQR = 1.900 ms, LowerFence = 13.716 ms, UpperFence = 21.317 ms
ConfidenceInterval = [17.195 ms; 18.229 ms] (CI 99.9%), Margin = 0.517 ms (2.92% of Mean)
Skewness = 0.85, Kurtosis = 2.71, MValue = 2.33

// ** Remained 7 (70.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:56 (0h 6m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=10000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 875a7855-6805-431f-81e6-09e019e75bd1.dll --anonymousPipes 656 1392 --benchmarkName "Sample.Benchmark.MessagePackVSJson.MpackSerializeAndDeserialize(Amount: 10000)" --job Default --benchmarkId 3 in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 176100.00 ns, 176.1000 us/op
WorkloadJitting  1: 1 op, 121478200.00 ns, 121.4782 ms/op

WorkloadPilot    1: 2 op, 29885300.00 ns, 14.9427 ms/op
WorkloadPilot    2: 3 op, 45672300.00 ns, 15.2241 ms/op
WorkloadPilot    3: 4 op, 62218300.00 ns, 15.5546 ms/op
WorkloadPilot    4: 5 op, 55236100.00 ns, 11.0472 ms/op
WorkloadPilot    5: 6 op, 17450600.00 ns, 2.9084 ms/op
WorkloadPilot    6: 7 op, 17566400.00 ns, 2.5095 ms/op
WorkloadPilot    7: 8 op, 21822200.00 ns, 2.7278 ms/op
WorkloadPilot    8: 9 op, 23810700.00 ns, 2.6456 ms/op
WorkloadPilot    9: 10 op, 27893700.00 ns, 2.7894 ms/op
WorkloadPilot   10: 11 op, 28842200.00 ns, 2.6220 ms/op
WorkloadPilot   11: 12 op, 32797500.00 ns, 2.7331 ms/op
WorkloadPilot   12: 13 op, 35152300.00 ns, 2.7040 ms/op
WorkloadPilot   13: 14 op, 37437800.00 ns, 2.6741 ms/op
WorkloadPilot   14: 15 op, 38572500.00 ns, 2.5715 ms/op
WorkloadPilot   15: 16 op, 40960400.00 ns, 2.5600 ms/op
WorkloadPilot   16: 32 op, 80535000.00 ns, 2.5167 ms/op
WorkloadPilot   17: 64 op, 158696700.00 ns, 2.4796 ms/op
WorkloadPilot   18: 128 op, 317825600.00 ns, 2.4830 ms/op
WorkloadPilot   19: 256 op, 635379300.00 ns, 2.4820 ms/op

WorkloadWarmup   1: 256 op, 615543900.00 ns, 2.4045 ms/op
WorkloadWarmup   2: 256 op, 631870100.00 ns, 2.4682 ms/op
WorkloadWarmup   3: 256 op, 623250100.00 ns, 2.4346 ms/op
WorkloadWarmup   4: 256 op, 605891700.00 ns, 2.3668 ms/op
WorkloadWarmup   5: 256 op, 614406000.00 ns, 2.4000 ms/op
WorkloadWarmup   6: 256 op, 608116200.00 ns, 2.3755 ms/op

// BeforeActualRun
WorkloadActual   1: 256 op, 642985300.00 ns, 2.5117 ms/op
WorkloadActual   2: 256 op, 628085100.00 ns, 2.4535 ms/op
WorkloadActual   3: 256 op, 619362200.00 ns, 2.4194 ms/op
WorkloadActual   4: 256 op, 670445500.00 ns, 2.6189 ms/op
WorkloadActual   5: 256 op, 693087400.00 ns, 2.7074 ms/op
WorkloadActual   6: 256 op, 722595900.00 ns, 2.8226 ms/op
WorkloadActual   7: 256 op, 721955200.00 ns, 2.8201 ms/op
WorkloadActual   8: 256 op, 934674300.00 ns, 3.6511 ms/op
WorkloadActual   9: 256 op, 722698400.00 ns, 2.8230 ms/op
WorkloadActual  10: 256 op, 644546400.00 ns, 2.5178 ms/op
WorkloadActual  11: 256 op, 648398700.00 ns, 2.5328 ms/op
WorkloadActual  12: 256 op, 831048500.00 ns, 3.2463 ms/op
WorkloadActual  13: 256 op, 747259000.00 ns, 2.9190 ms/op
WorkloadActual  14: 256 op, 670200400.00 ns, 2.6180 ms/op
WorkloadActual  15: 256 op, 730432100.00 ns, 2.8533 ms/op
WorkloadActual  16: 256 op, 649691000.00 ns, 2.5379 ms/op
WorkloadActual  17: 256 op, 617728100.00 ns, 2.4130 ms/op
WorkloadActual  18: 256 op, 609389600.00 ns, 2.3804 ms/op
WorkloadActual  19: 256 op, 618685300.00 ns, 2.4167 ms/op
WorkloadActual  20: 256 op, 615865200.00 ns, 2.4057 ms/op
WorkloadActual  21: 256 op, 683079300.00 ns, 2.6683 ms/op
WorkloadActual  22: 256 op, 661971200.00 ns, 2.5858 ms/op
WorkloadActual  23: 256 op, 616639800.00 ns, 2.4087 ms/op
WorkloadActual  24: 256 op, 624569400.00 ns, 2.4397 ms/op
WorkloadActual  25: 256 op, 627694900.00 ns, 2.4519 ms/op
WorkloadActual  26: 256 op, 618448400.00 ns, 2.4158 ms/op
WorkloadActual  27: 256 op, 624672900.00 ns, 2.4401 ms/op
WorkloadActual  28: 256 op, 623457200.00 ns, 2.4354 ms/op
WorkloadActual  29: 256 op, 662706400.00 ns, 2.5887 ms/op
WorkloadActual  30: 256 op, 695958500.00 ns, 2.7186 ms/op
WorkloadActual  31: 256 op, 662120800.00 ns, 2.5864 ms/op
WorkloadActual  32: 256 op, 657110800.00 ns, 2.5668 ms/op
WorkloadActual  33: 256 op, 680134500.00 ns, 2.6568 ms/op
WorkloadActual  34: 256 op, 727628700.00 ns, 2.8423 ms/op
WorkloadActual  35: 256 op, 665459200.00 ns, 2.5995 ms/op
WorkloadActual  36: 256 op, 651595800.00 ns, 2.5453 ms/op
WorkloadActual  37: 256 op, 669659400.00 ns, 2.6159 ms/op
WorkloadActual  38: 256 op, 655496200.00 ns, 2.5605 ms/op
WorkloadActual  39: 256 op, 711915500.00 ns, 2.7809 ms/op
WorkloadActual  40: 256 op, 681112600.00 ns, 2.6606 ms/op
WorkloadActual  41: 256 op, 651853000.00 ns, 2.5463 ms/op
WorkloadActual  42: 256 op, 654011500.00 ns, 2.5547 ms/op
WorkloadActual  43: 256 op, 664207800.00 ns, 2.5946 ms/op
WorkloadActual  44: 256 op, 648639800.00 ns, 2.5337 ms/op
WorkloadActual  45: 256 op, 721724300.00 ns, 2.8192 ms/op
WorkloadActual  46: 256 op, 943967800.00 ns, 3.6874 ms/op
WorkloadActual  47: 256 op, 666611100.00 ns, 2.6039 ms/op
WorkloadActual  48: 256 op, 898518500.00 ns, 3.5098 ms/op
WorkloadActual  49: 256 op, 877981100.00 ns, 3.4296 ms/op
WorkloadActual  50: 256 op, 1131008000.00 ns, 4.4180 ms/op
WorkloadActual  51: 256 op, 1165092200.00 ns, 4.5511 ms/op
WorkloadActual  52: 256 op, 1153823100.00 ns, 4.5071 ms/op
WorkloadActual  53: 256 op, 1010341200.00 ns, 3.9466 ms/op
WorkloadActual  54: 256 op, 941060400.00 ns, 3.6760 ms/op
WorkloadActual  55: 256 op, 964618300.00 ns, 3.7680 ms/op
WorkloadActual  56: 256 op, 792054900.00 ns, 3.0940 ms/op
WorkloadActual  57: 256 op, 960157500.00 ns, 3.7506 ms/op
WorkloadActual  58: 256 op, 962660600.00 ns, 3.7604 ms/op
WorkloadActual  59: 256 op, 913201100.00 ns, 3.5672 ms/op
WorkloadActual  60: 256 op, 823954300.00 ns, 3.2186 ms/op
WorkloadActual  61: 256 op, 879034900.00 ns, 3.4337 ms/op
WorkloadActual  62: 256 op, 1018514600.00 ns, 3.9786 ms/op
WorkloadActual  63: 256 op, 821317400.00 ns, 3.2083 ms/op
WorkloadActual  64: 256 op, 760693200.00 ns, 2.9715 ms/op
WorkloadActual  65: 256 op, 730582600.00 ns, 2.8538 ms/op
WorkloadActual  66: 256 op, 786789200.00 ns, 3.0734 ms/op
WorkloadActual  67: 256 op, 891775600.00 ns, 3.4835 ms/op
WorkloadActual  68: 256 op, 778921000.00 ns, 3.0427 ms/op
WorkloadActual  69: 256 op, 848092700.00 ns, 3.3129 ms/op
WorkloadActual  70: 256 op, 832037100.00 ns, 3.2501 ms/op
WorkloadActual  71: 256 op, 889518000.00 ns, 3.4747 ms/op
WorkloadActual  72: 256 op, 800938700.00 ns, 3.1287 ms/op
WorkloadActual  73: 256 op, 725776700.00 ns, 2.8351 ms/op
WorkloadActual  74: 256 op, 699428700.00 ns, 2.7321 ms/op
WorkloadActual  75: 256 op, 723280800.00 ns, 2.8253 ms/op
WorkloadActual  76: 256 op, 815807100.00 ns, 3.1867 ms/op
WorkloadActual  77: 256 op, 769737000.00 ns, 3.0068 ms/op
WorkloadActual  78: 256 op, 706116200.00 ns, 2.7583 ms/op
WorkloadActual  79: 256 op, 744726100.00 ns, 2.9091 ms/op
WorkloadActual  80: 256 op, 743946800.00 ns, 2.9060 ms/op
WorkloadActual  81: 256 op, 986780200.00 ns, 3.8546 ms/op
WorkloadActual  82: 256 op, 1143523500.00 ns, 4.4669 ms/op
WorkloadActual  83: 256 op, 1075905800.00 ns, 4.2028 ms/op
WorkloadActual  84: 256 op, 1069015600.00 ns, 4.1758 ms/op
WorkloadActual  85: 256 op, 833245600.00 ns, 3.2549 ms/op
WorkloadActual  86: 256 op, 741310000.00 ns, 2.8957 ms/op
WorkloadActual  87: 256 op, 713480400.00 ns, 2.7870 ms/op
WorkloadActual  88: 256 op, 713245500.00 ns, 2.7861 ms/op
WorkloadActual  89: 256 op, 750521600.00 ns, 2.9317 ms/op
WorkloadActual  90: 256 op, 753150900.00 ns, 2.9420 ms/op
WorkloadActual  91: 256 op, 752542300.00 ns, 2.9396 ms/op
WorkloadActual  92: 256 op, 692217500.00 ns, 2.7040 ms/op
WorkloadActual  93: 256 op, 671208100.00 ns, 2.6219 ms/op
WorkloadActual  94: 256 op, 684219100.00 ns, 2.6727 ms/op
WorkloadActual  95: 256 op, 726613300.00 ns, 2.8383 ms/op
WorkloadActual  96: 256 op, 646841300.00 ns, 2.5267 ms/op
WorkloadActual  97: 256 op, 673210000.00 ns, 2.6297 ms/op
WorkloadActual  98: 256 op, 644180100.00 ns, 2.5163 ms/op
WorkloadActual  99: 256 op, 647619500.00 ns, 2.5298 ms/op
WorkloadActual  100: 256 op, 651863500.00 ns, 2.5463 ms/op

// AfterActualRun
WorkloadResult   1: 256 op, 642985300.00 ns, 2.5117 ms/op
WorkloadResult   2: 256 op, 628085100.00 ns, 2.4535 ms/op
WorkloadResult   3: 256 op, 619362200.00 ns, 2.4194 ms/op
WorkloadResult   4: 256 op, 670445500.00 ns, 2.6189 ms/op
WorkloadResult   5: 256 op, 693087400.00 ns, 2.7074 ms/op
WorkloadResult   6: 256 op, 722595900.00 ns, 2.8226 ms/op
WorkloadResult   7: 256 op, 721955200.00 ns, 2.8201 ms/op
WorkloadResult   8: 256 op, 934674300.00 ns, 3.6511 ms/op
WorkloadResult   9: 256 op, 722698400.00 ns, 2.8230 ms/op
WorkloadResult  10: 256 op, 644546400.00 ns, 2.5178 ms/op
WorkloadResult  11: 256 op, 648398700.00 ns, 2.5328 ms/op
WorkloadResult  12: 256 op, 831048500.00 ns, 3.2463 ms/op
WorkloadResult  13: 256 op, 747259000.00 ns, 2.9190 ms/op
WorkloadResult  14: 256 op, 670200400.00 ns, 2.6180 ms/op
WorkloadResult  15: 256 op, 730432100.00 ns, 2.8533 ms/op
WorkloadResult  16: 256 op, 649691000.00 ns, 2.5379 ms/op
WorkloadResult  17: 256 op, 617728100.00 ns, 2.4130 ms/op
WorkloadResult  18: 256 op, 609389600.00 ns, 2.3804 ms/op
WorkloadResult  19: 256 op, 618685300.00 ns, 2.4167 ms/op
WorkloadResult  20: 256 op, 615865200.00 ns, 2.4057 ms/op
WorkloadResult  21: 256 op, 683079300.00 ns, 2.6683 ms/op
WorkloadResult  22: 256 op, 661971200.00 ns, 2.5858 ms/op
WorkloadResult  23: 256 op, 616639800.00 ns, 2.4087 ms/op
WorkloadResult  24: 256 op, 624569400.00 ns, 2.4397 ms/op
WorkloadResult  25: 256 op, 627694900.00 ns, 2.4519 ms/op
WorkloadResult  26: 256 op, 618448400.00 ns, 2.4158 ms/op
WorkloadResult  27: 256 op, 624672900.00 ns, 2.4401 ms/op
WorkloadResult  28: 256 op, 623457200.00 ns, 2.4354 ms/op
WorkloadResult  29: 256 op, 662706400.00 ns, 2.5887 ms/op
WorkloadResult  30: 256 op, 695958500.00 ns, 2.7186 ms/op
WorkloadResult  31: 256 op, 662120800.00 ns, 2.5864 ms/op
WorkloadResult  32: 256 op, 657110800.00 ns, 2.5668 ms/op
WorkloadResult  33: 256 op, 680134500.00 ns, 2.6568 ms/op
WorkloadResult  34: 256 op, 727628700.00 ns, 2.8423 ms/op
WorkloadResult  35: 256 op, 665459200.00 ns, 2.5995 ms/op
WorkloadResult  36: 256 op, 651595800.00 ns, 2.5453 ms/op
WorkloadResult  37: 256 op, 669659400.00 ns, 2.6159 ms/op
WorkloadResult  38: 256 op, 655496200.00 ns, 2.5605 ms/op
WorkloadResult  39: 256 op, 711915500.00 ns, 2.7809 ms/op
WorkloadResult  40: 256 op, 681112600.00 ns, 2.6606 ms/op
WorkloadResult  41: 256 op, 651853000.00 ns, 2.5463 ms/op
WorkloadResult  42: 256 op, 654011500.00 ns, 2.5547 ms/op
WorkloadResult  43: 256 op, 664207800.00 ns, 2.5946 ms/op
WorkloadResult  44: 256 op, 648639800.00 ns, 2.5337 ms/op
WorkloadResult  45: 256 op, 721724300.00 ns, 2.8192 ms/op
WorkloadResult  46: 256 op, 943967800.00 ns, 3.6874 ms/op
WorkloadResult  47: 256 op, 666611100.00 ns, 2.6039 ms/op
WorkloadResult  48: 256 op, 898518500.00 ns, 3.5098 ms/op
WorkloadResult  49: 256 op, 877981100.00 ns, 3.4296 ms/op
WorkloadResult  50: 256 op, 1010341200.00 ns, 3.9466 ms/op
WorkloadResult  51: 256 op, 941060400.00 ns, 3.6760 ms/op
WorkloadResult  52: 256 op, 964618300.00 ns, 3.7680 ms/op
WorkloadResult  53: 256 op, 792054900.00 ns, 3.0940 ms/op
WorkloadResult  54: 256 op, 960157500.00 ns, 3.7506 ms/op
WorkloadResult  55: 256 op, 962660600.00 ns, 3.7604 ms/op
WorkloadResult  56: 256 op, 913201100.00 ns, 3.5672 ms/op
WorkloadResult  57: 256 op, 823954300.00 ns, 3.2186 ms/op
WorkloadResult  58: 256 op, 879034900.00 ns, 3.4337 ms/op
WorkloadResult  59: 256 op, 1018514600.00 ns, 3.9786 ms/op
WorkloadResult  60: 256 op, 821317400.00 ns, 3.2083 ms/op
WorkloadResult  61: 256 op, 760693200.00 ns, 2.9715 ms/op
WorkloadResult  62: 256 op, 730582600.00 ns, 2.8538 ms/op
WorkloadResult  63: 256 op, 786789200.00 ns, 3.0734 ms/op
WorkloadResult  64: 256 op, 891775600.00 ns, 3.4835 ms/op
WorkloadResult  65: 256 op, 778921000.00 ns, 3.0427 ms/op
WorkloadResult  66: 256 op, 848092700.00 ns, 3.3129 ms/op
WorkloadResult  67: 256 op, 832037100.00 ns, 3.2501 ms/op
WorkloadResult  68: 256 op, 889518000.00 ns, 3.4747 ms/op
WorkloadResult  69: 256 op, 800938700.00 ns, 3.1287 ms/op
WorkloadResult  70: 256 op, 725776700.00 ns, 2.8351 ms/op
WorkloadResult  71: 256 op, 699428700.00 ns, 2.7321 ms/op
WorkloadResult  72: 256 op, 723280800.00 ns, 2.8253 ms/op
WorkloadResult  73: 256 op, 815807100.00 ns, 3.1867 ms/op
WorkloadResult  74: 256 op, 769737000.00 ns, 3.0068 ms/op
WorkloadResult  75: 256 op, 706116200.00 ns, 2.7583 ms/op
WorkloadResult  76: 256 op, 744726100.00 ns, 2.9091 ms/op
WorkloadResult  77: 256 op, 743946800.00 ns, 2.9060 ms/op
WorkloadResult  78: 256 op, 986780200.00 ns, 3.8546 ms/op
WorkloadResult  79: 256 op, 1075905800.00 ns, 4.2028 ms/op
WorkloadResult  80: 256 op, 1069015600.00 ns, 4.1758 ms/op
WorkloadResult  81: 256 op, 833245600.00 ns, 3.2549 ms/op
WorkloadResult  82: 256 op, 741310000.00 ns, 2.8957 ms/op
WorkloadResult  83: 256 op, 713480400.00 ns, 2.7870 ms/op
WorkloadResult  84: 256 op, 713245500.00 ns, 2.7861 ms/op
WorkloadResult  85: 256 op, 750521600.00 ns, 2.9317 ms/op
WorkloadResult  86: 256 op, 753150900.00 ns, 2.9420 ms/op
WorkloadResult  87: 256 op, 752542300.00 ns, 2.9396 ms/op
WorkloadResult  88: 256 op, 692217500.00 ns, 2.7040 ms/op
WorkloadResult  89: 256 op, 671208100.00 ns, 2.6219 ms/op
WorkloadResult  90: 256 op, 684219100.00 ns, 2.6727 ms/op
WorkloadResult  91: 256 op, 726613300.00 ns, 2.8383 ms/op
WorkloadResult  92: 256 op, 646841300.00 ns, 2.5267 ms/op
WorkloadResult  93: 256 op, 673210000.00 ns, 2.6297 ms/op
WorkloadResult  94: 256 op, 644180100.00 ns, 2.5163 ms/op
WorkloadResult  95: 256 op, 647619500.00 ns, 2.5298 ms/op
WorkloadResult  96: 256 op, 651863500.00 ns, 2.5463 ms/op
// GC:  83 74 66 568549928 256
// Threading:  0 0 256

// AfterAll
// Benchmark Process 13912 has exited with code 0.

Mean = 2.907 ms, StdErr = 0.046 ms (1.59%), N = 96, StdDev = 0.453 ms
Min = 2.380 ms, Q1 = 2.559 ms, Median = 2.787 ms, Q3 = 3.143 ms, Max = 4.203 ms
IQR = 0.584 ms, LowerFence = 1.683 ms, UpperFence = 4.019 ms
ConfidenceInterval = [2.749 ms; 3.064 ms] (CI 99.9%), Margin = 0.157 ms (5.41% of Mean)
Skewness = 1.08, Kurtosis = 3.25, MValue = 2.45

// ** Remained 6 (60.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:58 (0h 6m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.MpackSerializeAndDeserializeV2: DefaultJob [Amount=10000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 875a7855-6805-431f-81e6-09e019e75bd1.dll --anonymousPipes 1460 1428 --benchmarkName "Sample.Benchmark.MessagePackVSJson.MpackSerializeAndDeserializeV2(Amount: 10000)" --job Default --benchmarkId 4 in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 184300.00 ns, 184.3000 us/op

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> MessagePack.MessagePackSerializationException: Failed to serialize System.Collections.Generic.List`1[[Sample.Benchmark.MessageItem, Sample.Benchmark, Version=*******, Culture=neutral, PublicKeyToken=null]] value.
 ---> MessagePack.FormatterNotRegisteredException: Sample.Benchmark.MessageItem is not registered in resolver: MessagePack.Resolvers.StandardResolver
   at MessagePack.FormatterResolverExtensions.Throw(Type t, IFormatterResolver resolver)
   at MessagePack.Formatters.ListFormatter`1.Serialize(MessagePackWriter& writer, List`1 value, MessagePackSerializerOptions options)
   at MessagePack.MessagePackSerializer.Serialize[T](MessagePackWriter& writer, T value, MessagePackSerializerOptions options)
   --- End of inner exception stack trace ---
   at MessagePack.MessagePackSerializer.Serialize[T](MessagePackWriter& writer, T value, MessagePackSerializerOptions options)
   at MessagePack.MessagePackSerializer.Serialize[T](T value, MessagePackSerializerOptions options, CancellationToken cancellationToken)
   at Sample.Benchmark.MessagePackVSJson.MpackSerializeAndDeserializeV2() in D:\代码\Sample\Sample.Benchmark\MessagePackVSJson.cs:line 76
   at BenchmarkDotNet.Autogenerated.Runnable_4.WorkloadActionNoUnroll(Int64 invokeCount) in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\875a7855-6805-431f-81e6-09e019e75bd1.notcs:line 1079
   at BenchmarkDotNet.Engines.Engine.Measure(Action`1 action, Int64 invokeCount)
   at BenchmarkDotNet.Engines.Engine.RunIteration(IterationData data)
   at BenchmarkDotNet.Engines.EngineFactory.Jit(Engine engine, Int32 jitIndex, Int32 invokeCount, Int32 unrollFactor)
   at BenchmarkDotNet.Engines.EngineFactory.CreateReadyToRun(EngineParameters engineParameters)
   at BenchmarkDotNet.Autogenerated.Runnable_4.Run(IHost host, String benchmarkName) in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\875a7855-6805-431f-81e6-09e019e75bd1.notcs:line 944
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   --- End of inner exception stack trace ---
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at BenchmarkDotNet.Autogenerated.UniqueProgramName.AfterAssemblyLoadingAttached(String[] args) in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\875a7855-6805-431f-81e6-09e019e75bd1.notcs:line 57
// AfterAll
No Workload Results were obtained from the run.
// Benchmark Process 12320 has exited with code -1.

// ** Remained 5 (50.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:55 (0h 4m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.JsonSerialize: DefaultJob [Amount=100000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 875a7855-6805-431f-81e6-09e019e75bd1.dll --anonymousPipes 1540 1400 --benchmarkName "Sample.Benchmark.MessagePackVSJson.JsonSerialize(Amount: 100000)" --job Default --benchmarkId 5 in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 189900.00 ns, 189.9000 us/op
WorkloadJitting  1: 1 op, 283091500.00 ns, 283.0915 ms/op

WorkloadPilot    1: 2 op, 119874400.00 ns, 59.9372 ms/op
WorkloadPilot    2: 3 op, 205916600.00 ns, 68.6389 ms/op
WorkloadPilot    3: 4 op, 240757700.00 ns, 60.1894 ms/op
WorkloadPilot    4: 5 op, 319174600.00 ns, 63.8349 ms/op
WorkloadPilot    5: 6 op, 358477100.00 ns, 59.7462 ms/op
WorkloadPilot    6: 7 op, 433471800.00 ns, 61.9245 ms/op
WorkloadPilot    7: 8 op, 507164900.00 ns, 63.3956 ms/op

WorkloadWarmup   1: 8 op, 537986700.00 ns, 67.2483 ms/op
WorkloadWarmup   2: 8 op, 526016100.00 ns, 65.7520 ms/op
WorkloadWarmup   3: 8 op, 535210900.00 ns, 66.9014 ms/op
WorkloadWarmup   4: 8 op, 537324600.00 ns, 67.1656 ms/op
WorkloadWarmup   5: 8 op, 568827300.00 ns, 71.1034 ms/op
WorkloadWarmup   6: 8 op, 504178600.00 ns, 63.0223 ms/op
WorkloadWarmup   7: 8 op, 506584900.00 ns, 63.3231 ms/op
WorkloadWarmup   8: 8 op, 547765900.00 ns, 68.4707 ms/op
WorkloadWarmup   9: 8 op, 516920900.00 ns, 64.6151 ms/op

// BeforeActualRun
WorkloadActual   1: 8 op, 538856500.00 ns, 67.3571 ms/op
WorkloadActual   2: 8 op, 478144200.00 ns, 59.7680 ms/op
WorkloadActual   3: 8 op, 550987300.00 ns, 68.8734 ms/op
WorkloadActual   4: 8 op, 565167100.00 ns, 70.6459 ms/op
WorkloadActual   5: 8 op, 578223900.00 ns, 72.2780 ms/op
WorkloadActual   6: 8 op, 550913200.00 ns, 68.8642 ms/op
WorkloadActual   7: 8 op, 540944700.00 ns, 67.6181 ms/op
WorkloadActual   8: 8 op, 492565300.00 ns, 61.5707 ms/op
WorkloadActual   9: 8 op, 513333600.00 ns, 64.1667 ms/op
WorkloadActual  10: 8 op, 495867400.00 ns, 61.9834 ms/op
WorkloadActual  11: 8 op, 474403600.00 ns, 59.3005 ms/op
WorkloadActual  12: 8 op, 560708100.00 ns, 70.0885 ms/op
WorkloadActual  13: 8 op, 769463000.00 ns, 96.1829 ms/op
WorkloadActual  14: 8 op, 742313700.00 ns, 92.7892 ms/op
WorkloadActual  15: 8 op, 673923800.00 ns, 84.2405 ms/op
WorkloadActual  16: 8 op, 582824600.00 ns, 72.8531 ms/op
WorkloadActual  17: 8 op, 596610200.00 ns, 74.5763 ms/op
WorkloadActual  18: 8 op, 616135400.00 ns, 77.0169 ms/op
WorkloadActual  19: 8 op, 577272400.00 ns, 72.1591 ms/op
WorkloadActual  20: 8 op, 571728000.00 ns, 71.4660 ms/op
WorkloadActual  21: 8 op, 555517700.00 ns, 69.4397 ms/op
WorkloadActual  22: 8 op, 524572400.00 ns, 65.5716 ms/op
WorkloadActual  23: 8 op, 552421500.00 ns, 69.0527 ms/op
WorkloadActual  24: 8 op, 650782200.00 ns, 81.3478 ms/op
WorkloadActual  25: 8 op, 620565800.00 ns, 77.5707 ms/op
WorkloadActual  26: 8 op, 684790900.00 ns, 85.5989 ms/op
WorkloadActual  27: 8 op, 553525800.00 ns, 69.1907 ms/op
WorkloadActual  28: 8 op, 566238400.00 ns, 70.7798 ms/op
WorkloadActual  29: 8 op, 560908500.00 ns, 70.1136 ms/op
WorkloadActual  30: 8 op, 536853900.00 ns, 67.1067 ms/op
WorkloadActual  31: 8 op, 553171300.00 ns, 69.1464 ms/op
WorkloadActual  32: 8 op, 531497500.00 ns, 66.4372 ms/op
WorkloadActual  33: 8 op, 533707000.00 ns, 66.7134 ms/op
WorkloadActual  34: 8 op, 540696600.00 ns, 67.5871 ms/op
WorkloadActual  35: 8 op, 566406300.00 ns, 70.8008 ms/op
WorkloadActual  36: 8 op, 559881400.00 ns, 69.9852 ms/op
WorkloadActual  37: 8 op, 558627000.00 ns, 69.8284 ms/op
WorkloadActual  38: 8 op, 535236700.00 ns, 66.9046 ms/op
WorkloadActual  39: 8 op, 581781700.00 ns, 72.7227 ms/op
WorkloadActual  40: 8 op, 587106000.00 ns, 73.3883 ms/op
WorkloadActual  41: 8 op, 727404500.00 ns, 90.9256 ms/op
WorkloadActual  42: 8 op, 749466900.00 ns, 93.6834 ms/op
WorkloadActual  43: 8 op, 698278900.00 ns, 87.2849 ms/op
WorkloadActual  44: 8 op, 686564900.00 ns, 85.8206 ms/op
WorkloadActual  45: 8 op, 624932100.00 ns, 78.1165 ms/op
WorkloadActual  46: 8 op, 656549200.00 ns, 82.0687 ms/op
WorkloadActual  47: 8 op, 634814900.00 ns, 79.3519 ms/op
WorkloadActual  48: 8 op, 693167200.00 ns, 86.6459 ms/op
WorkloadActual  49: 8 op, 603442200.00 ns, 75.4303 ms/op
WorkloadActual  50: 8 op, 551175800.00 ns, 68.8970 ms/op
WorkloadActual  51: 8 op, 534385800.00 ns, 66.7982 ms/op
WorkloadActual  52: 8 op, 536350700.00 ns, 67.0438 ms/op
WorkloadActual  53: 8 op, 566926300.00 ns, 70.8658 ms/op
WorkloadActual  54: 8 op, 567638400.00 ns, 70.9548 ms/op
WorkloadActual  55: 8 op, 573744000.00 ns, 71.7180 ms/op
WorkloadActual  56: 8 op, 565946300.00 ns, 70.7433 ms/op
WorkloadActual  57: 8 op, 559796200.00 ns, 69.9745 ms/op
WorkloadActual  58: 8 op, 550202800.00 ns, 68.7754 ms/op
WorkloadActual  59: 8 op, 572183400.00 ns, 71.5229 ms/op
WorkloadActual  60: 8 op, 555618700.00 ns, 69.4523 ms/op
WorkloadActual  61: 8 op, 556012100.00 ns, 69.5015 ms/op
WorkloadActual  62: 8 op, 523610500.00 ns, 65.4513 ms/op
WorkloadActual  63: 8 op, 621295100.00 ns, 77.6619 ms/op
WorkloadActual  64: 8 op, 654012100.00 ns, 81.7515 ms/op
WorkloadActual  65: 8 op, 632348800.00 ns, 79.0436 ms/op
WorkloadActual  66: 8 op, 676208500.00 ns, 84.5261 ms/op
WorkloadActual  67: 8 op, 677822300.00 ns, 84.7278 ms/op
WorkloadActual  68: 8 op, 593546700.00 ns, 74.1933 ms/op
WorkloadActual  69: 8 op, 564332600.00 ns, 70.5416 ms/op
WorkloadActual  70: 8 op, 541984500.00 ns, 67.7481 ms/op
WorkloadActual  71: 8 op, 596082100.00 ns, 74.5103 ms/op
WorkloadActual  72: 8 op, 585500200.00 ns, 73.1875 ms/op
WorkloadActual  73: 8 op, 513917800.00 ns, 64.2397 ms/op
WorkloadActual  74: 8 op, 486668300.00 ns, 60.8335 ms/op
WorkloadActual  75: 8 op, 510408700.00 ns, 63.8011 ms/op
WorkloadActual  76: 8 op, 525463400.00 ns, 65.6829 ms/op
WorkloadActual  77: 8 op, 569136600.00 ns, 71.1421 ms/op
WorkloadActual  78: 8 op, 521488100.00 ns, 65.1860 ms/op
WorkloadActual  79: 8 op, 507599900.00 ns, 63.4500 ms/op
WorkloadActual  80: 8 op, 507967500.00 ns, 63.4959 ms/op
WorkloadActual  81: 8 op, 544373200.00 ns, 68.0467 ms/op
WorkloadActual  82: 8 op, 530756100.00 ns, 66.3445 ms/op
WorkloadActual  83: 8 op, 510219200.00 ns, 63.7774 ms/op
WorkloadActual  84: 8 op, 509569100.00 ns, 63.6961 ms/op
WorkloadActual  85: 8 op, 535944400.00 ns, 66.9931 ms/op
WorkloadActual  86: 8 op, 573653100.00 ns, 71.7066 ms/op
WorkloadActual  87: 8 op, 561220100.00 ns, 70.1525 ms/op
WorkloadActual  88: 8 op, 561047600.00 ns, 70.1310 ms/op
WorkloadActual  89: 8 op, 590076900.00 ns, 73.7596 ms/op
WorkloadActual  90: 8 op, 517189000.00 ns, 64.6486 ms/op
WorkloadActual  91: 8 op, 521719600.00 ns, 65.2150 ms/op
WorkloadActual  92: 8 op, 539744800.00 ns, 67.4681 ms/op
WorkloadActual  93: 8 op, 508698200.00 ns, 63.5873 ms/op
WorkloadActual  94: 8 op, 509924400.00 ns, 63.7406 ms/op
WorkloadActual  95: 8 op, 507700000.00 ns, 63.4625 ms/op
WorkloadActual  96: 8 op, 505932100.00 ns, 63.2415 ms/op
WorkloadActual  97: 8 op, 477201900.00 ns, 59.6502 ms/op
WorkloadActual  98: 8 op, 491535700.00 ns, 61.4420 ms/op
WorkloadActual  99: 8 op, 526246900.00 ns, 65.7809 ms/op
WorkloadActual  100: 8 op, 526115900.00 ns, 65.7645 ms/op

// AfterActualRun
WorkloadResult   1: 8 op, 538856500.00 ns, 67.3571 ms/op
WorkloadResult   2: 8 op, 478144200.00 ns, 59.7680 ms/op
WorkloadResult   3: 8 op, 550987300.00 ns, 68.8734 ms/op
WorkloadResult   4: 8 op, 565167100.00 ns, 70.6459 ms/op
WorkloadResult   5: 8 op, 578223900.00 ns, 72.2780 ms/op
WorkloadResult   6: 8 op, 550913200.00 ns, 68.8642 ms/op
WorkloadResult   7: 8 op, 540944700.00 ns, 67.6181 ms/op
WorkloadResult   8: 8 op, 492565300.00 ns, 61.5707 ms/op
WorkloadResult   9: 8 op, 513333600.00 ns, 64.1667 ms/op
WorkloadResult  10: 8 op, 495867400.00 ns, 61.9834 ms/op
WorkloadResult  11: 8 op, 474403600.00 ns, 59.3005 ms/op
WorkloadResult  12: 8 op, 560708100.00 ns, 70.0885 ms/op
WorkloadResult  13: 8 op, 673923800.00 ns, 84.2405 ms/op
WorkloadResult  14: 8 op, 582824600.00 ns, 72.8531 ms/op
WorkloadResult  15: 8 op, 596610200.00 ns, 74.5763 ms/op
WorkloadResult  16: 8 op, 616135400.00 ns, 77.0169 ms/op
WorkloadResult  17: 8 op, 577272400.00 ns, 72.1591 ms/op
WorkloadResult  18: 8 op, 571728000.00 ns, 71.4660 ms/op
WorkloadResult  19: 8 op, 555517700.00 ns, 69.4397 ms/op
WorkloadResult  20: 8 op, 524572400.00 ns, 65.5716 ms/op
WorkloadResult  21: 8 op, 552421500.00 ns, 69.0527 ms/op
WorkloadResult  22: 8 op, 650782200.00 ns, 81.3478 ms/op
WorkloadResult  23: 8 op, 620565800.00 ns, 77.5707 ms/op
WorkloadResult  24: 8 op, 553525800.00 ns, 69.1907 ms/op
WorkloadResult  25: 8 op, 566238400.00 ns, 70.7798 ms/op
WorkloadResult  26: 8 op, 560908500.00 ns, 70.1136 ms/op
WorkloadResult  27: 8 op, 536853900.00 ns, 67.1067 ms/op
WorkloadResult  28: 8 op, 553171300.00 ns, 69.1464 ms/op
WorkloadResult  29: 8 op, 531497500.00 ns, 66.4372 ms/op
WorkloadResult  30: 8 op, 533707000.00 ns, 66.7134 ms/op
WorkloadResult  31: 8 op, 540696600.00 ns, 67.5871 ms/op
WorkloadResult  32: 8 op, 566406300.00 ns, 70.8008 ms/op
WorkloadResult  33: 8 op, 559881400.00 ns, 69.9852 ms/op
WorkloadResult  34: 8 op, 558627000.00 ns, 69.8284 ms/op
WorkloadResult  35: 8 op, 535236700.00 ns, 66.9046 ms/op
WorkloadResult  36: 8 op, 581781700.00 ns, 72.7227 ms/op
WorkloadResult  37: 8 op, 587106000.00 ns, 73.3883 ms/op
WorkloadResult  38: 8 op, 624932100.00 ns, 78.1165 ms/op
WorkloadResult  39: 8 op, 656549200.00 ns, 82.0687 ms/op
WorkloadResult  40: 8 op, 634814900.00 ns, 79.3519 ms/op
WorkloadResult  41: 8 op, 603442200.00 ns, 75.4303 ms/op
WorkloadResult  42: 8 op, 551175800.00 ns, 68.8970 ms/op
WorkloadResult  43: 8 op, 534385800.00 ns, 66.7982 ms/op
WorkloadResult  44: 8 op, 536350700.00 ns, 67.0438 ms/op
WorkloadResult  45: 8 op, 566926300.00 ns, 70.8658 ms/op
WorkloadResult  46: 8 op, 567638400.00 ns, 70.9548 ms/op
WorkloadResult  47: 8 op, 573744000.00 ns, 71.7180 ms/op
WorkloadResult  48: 8 op, 565946300.00 ns, 70.7433 ms/op
WorkloadResult  49: 8 op, 559796200.00 ns, 69.9745 ms/op
WorkloadResult  50: 8 op, 550202800.00 ns, 68.7754 ms/op
WorkloadResult  51: 8 op, 572183400.00 ns, 71.5229 ms/op
WorkloadResult  52: 8 op, 555618700.00 ns, 69.4523 ms/op
WorkloadResult  53: 8 op, 556012100.00 ns, 69.5015 ms/op
WorkloadResult  54: 8 op, 523610500.00 ns, 65.4513 ms/op
WorkloadResult  55: 8 op, 621295100.00 ns, 77.6619 ms/op
WorkloadResult  56: 8 op, 654012100.00 ns, 81.7515 ms/op
WorkloadResult  57: 8 op, 632348800.00 ns, 79.0436 ms/op
WorkloadResult  58: 8 op, 676208500.00 ns, 84.5261 ms/op
WorkloadResult  59: 8 op, 677822300.00 ns, 84.7278 ms/op
WorkloadResult  60: 8 op, 593546700.00 ns, 74.1933 ms/op
WorkloadResult  61: 8 op, 564332600.00 ns, 70.5416 ms/op
WorkloadResult  62: 8 op, 541984500.00 ns, 67.7481 ms/op
WorkloadResult  63: 8 op, 596082100.00 ns, 74.5103 ms/op
WorkloadResult  64: 8 op, 585500200.00 ns, 73.1875 ms/op
WorkloadResult  65: 8 op, 513917800.00 ns, 64.2397 ms/op
WorkloadResult  66: 8 op, 486668300.00 ns, 60.8335 ms/op
WorkloadResult  67: 8 op, 510408700.00 ns, 63.8011 ms/op
WorkloadResult  68: 8 op, 525463400.00 ns, 65.6829 ms/op
WorkloadResult  69: 8 op, 569136600.00 ns, 71.1421 ms/op
WorkloadResult  70: 8 op, 521488100.00 ns, 65.1860 ms/op
WorkloadResult  71: 8 op, 507599900.00 ns, 63.4500 ms/op
WorkloadResult  72: 8 op, 507967500.00 ns, 63.4959 ms/op
WorkloadResult  73: 8 op, 544373200.00 ns, 68.0467 ms/op
WorkloadResult  74: 8 op, 530756100.00 ns, 66.3445 ms/op
WorkloadResult  75: 8 op, 510219200.00 ns, 63.7774 ms/op
WorkloadResult  76: 8 op, 509569100.00 ns, 63.6961 ms/op
WorkloadResult  77: 8 op, 535944400.00 ns, 66.9931 ms/op
WorkloadResult  78: 8 op, 573653100.00 ns, 71.7066 ms/op
WorkloadResult  79: 8 op, 561220100.00 ns, 70.1525 ms/op
WorkloadResult  80: 8 op, 561047600.00 ns, 70.1310 ms/op
WorkloadResult  81: 8 op, 590076900.00 ns, 73.7596 ms/op
WorkloadResult  82: 8 op, 517189000.00 ns, 64.6486 ms/op
WorkloadResult  83: 8 op, 521719600.00 ns, 65.2150 ms/op
WorkloadResult  84: 8 op, 539744800.00 ns, 67.4681 ms/op
WorkloadResult  85: 8 op, 508698200.00 ns, 63.5873 ms/op
WorkloadResult  86: 8 op, 509924400.00 ns, 63.7406 ms/op
WorkloadResult  87: 8 op, 507700000.00 ns, 63.4625 ms/op
WorkloadResult  88: 8 op, 505932100.00 ns, 63.2415 ms/op
WorkloadResult  89: 8 op, 477201900.00 ns, 59.6502 ms/op
WorkloadResult  90: 8 op, 491535700.00 ns, 61.4420 ms/op
WorkloadResult  91: 8 op, 526246900.00 ns, 65.7809 ms/op
WorkloadResult  92: 8 op, 526115900.00 ns, 65.7645 ms/op
// GC:  48 31 12 522649912 8
// Threading:  0 0 8

// AfterAll
// Benchmark Process 25676 has exited with code 0.

Mean = 69.560 ms, StdErr = 0.593 ms (0.85%), N = 92, StdDev = 5.686 ms
Min = 59.300 ms, Q1 = 65.655 ms, Median = 69.169 ms, Q3 = 71.828 ms, Max = 84.728 ms
IQR = 6.173 ms, LowerFence = 56.395 ms, UpperFence = 81.088 ms
ConfidenceInterval = [67.544 ms; 71.576 ms] (CI 99.9%), Margin = 2.016 ms (2.90% of Mean)
Skewness = 0.71, Kurtosis = 3.3, MValue = 2.36

// ** Remained 4 (40.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:56 (0h 3m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.MpackSerialize: DefaultJob [Amount=100000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 875a7855-6805-431f-81e6-09e019e75bd1.dll --anonymousPipes 1532 1368 --benchmarkName "Sample.Benchmark.MessagePackVSJson.MpackSerialize(Amount: 100000)" --job Default --benchmarkId 6 in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 178800.00 ns, 178.8000 us/op
WorkloadJitting  1: 1 op, 129365100.00 ns, 129.3651 ms/op

WorkloadPilot    1: 2 op, 62058900.00 ns, 31.0295 ms/op
WorkloadPilot    2: 3 op, 95400000.00 ns, 31.8000 ms/op
WorkloadPilot    3: 4 op, 64261200.00 ns, 16.0653 ms/op
WorkloadPilot    4: 5 op, 57297200.00 ns, 11.4594 ms/op
WorkloadPilot    5: 6 op, 67179500.00 ns, 11.1966 ms/op
WorkloadPilot    6: 7 op, 79685400.00 ns, 11.3836 ms/op
WorkloadPilot    7: 8 op, 92627100.00 ns, 11.5784 ms/op
WorkloadPilot    8: 9 op, 102475900.00 ns, 11.3862 ms/op
WorkloadPilot    9: 10 op, 115530600.00 ns, 11.5531 ms/op
WorkloadPilot   10: 11 op, 126138300.00 ns, 11.4671 ms/op
WorkloadPilot   11: 12 op, 132018500.00 ns, 11.0015 ms/op
WorkloadPilot   12: 13 op, 149734000.00 ns, 11.5180 ms/op
WorkloadPilot   13: 14 op, 154246200.00 ns, 11.0176 ms/op
WorkloadPilot   14: 15 op, 181141800.00 ns, 12.0761 ms/op
WorkloadPilot   15: 16 op, 186146300.00 ns, 11.6341 ms/op
WorkloadPilot   16: 32 op, 382766500.00 ns, 11.9615 ms/op
WorkloadPilot   17: 64 op, 847883900.00 ns, 13.2482 ms/op

WorkloadWarmup   1: 64 op, 761734300.00 ns, 11.9021 ms/op
WorkloadWarmup   2: 64 op, 712367000.00 ns, 11.1307 ms/op
WorkloadWarmup   3: 64 op, 723836800.00 ns, 11.3100 ms/op
WorkloadWarmup   4: 64 op, 702895600.00 ns, 10.9827 ms/op
WorkloadWarmup   5: 64 op, 715925300.00 ns, 11.1863 ms/op
WorkloadWarmup   6: 64 op, 728835400.00 ns, 11.3881 ms/op
WorkloadWarmup   7: 64 op, 746751100.00 ns, 11.6680 ms/op
WorkloadWarmup   8: 64 op, 740794700.00 ns, 11.5749 ms/op

// BeforeActualRun
WorkloadActual   1: 64 op, 744740500.00 ns, 11.6366 ms/op
WorkloadActual   2: 64 op, 767280400.00 ns, 11.9888 ms/op
WorkloadActual   3: 64 op, 789051100.00 ns, 12.3289 ms/op
WorkloadActual   4: 64 op, 744158300.00 ns, 11.6275 ms/op
WorkloadActual   5: 64 op, 738897500.00 ns, 11.5453 ms/op
WorkloadActual   6: 64 op, 765663600.00 ns, 11.9635 ms/op
WorkloadActual   7: 64 op, 778389100.00 ns, 12.1623 ms/op
WorkloadActual   8: 64 op, 771280700.00 ns, 12.0513 ms/op
WorkloadActual   9: 64 op, 755294800.00 ns, 11.8015 ms/op
WorkloadActual  10: 64 op, 776397200.00 ns, 12.1312 ms/op
WorkloadActual  11: 64 op, 762033200.00 ns, 11.9068 ms/op
WorkloadActual  12: 64 op, 733054600.00 ns, 11.4540 ms/op
WorkloadActual  13: 64 op, 756103900.00 ns, 11.8141 ms/op
WorkloadActual  14: 64 op, 804277900.00 ns, 12.5668 ms/op
WorkloadActual  15: 64 op, 747446100.00 ns, 11.6788 ms/op
WorkloadActual  16: 64 op, 787233300.00 ns, 12.3005 ms/op
WorkloadActual  17: 64 op, 757076300.00 ns, 11.8293 ms/op
WorkloadActual  18: 64 op, 737604500.00 ns, 11.5251 ms/op
WorkloadActual  19: 64 op, 758248600.00 ns, 11.8476 ms/op
WorkloadActual  20: 64 op, 737257200.00 ns, 11.5196 ms/op
WorkloadActual  21: 64 op, 727538500.00 ns, 11.3678 ms/op
WorkloadActual  22: 64 op, 725999400.00 ns, 11.3437 ms/op
WorkloadActual  23: 64 op, 726194100.00 ns, 11.3468 ms/op
WorkloadActual  24: 64 op, 719804500.00 ns, 11.2469 ms/op
WorkloadActual  25: 64 op, 761419000.00 ns, 11.8972 ms/op
WorkloadActual  26: 64 op, 738636900.00 ns, 11.5412 ms/op
WorkloadActual  27: 64 op, 732264400.00 ns, 11.4416 ms/op
WorkloadActual  28: 64 op, 758948200.00 ns, 11.8586 ms/op

// AfterActualRun
WorkloadResult   1: 64 op, 744740500.00 ns, 11.6366 ms/op
WorkloadResult   2: 64 op, 767280400.00 ns, 11.9888 ms/op
WorkloadResult   3: 64 op, 789051100.00 ns, 12.3289 ms/op
WorkloadResult   4: 64 op, 744158300.00 ns, 11.6275 ms/op
WorkloadResult   5: 64 op, 738897500.00 ns, 11.5453 ms/op
WorkloadResult   6: 64 op, 765663600.00 ns, 11.9635 ms/op
WorkloadResult   7: 64 op, 778389100.00 ns, 12.1623 ms/op
WorkloadResult   8: 64 op, 771280700.00 ns, 12.0513 ms/op
WorkloadResult   9: 64 op, 755294800.00 ns, 11.8015 ms/op
WorkloadResult  10: 64 op, 776397200.00 ns, 12.1312 ms/op
WorkloadResult  11: 64 op, 762033200.00 ns, 11.9068 ms/op
WorkloadResult  12: 64 op, 733054600.00 ns, 11.4540 ms/op
WorkloadResult  13: 64 op, 756103900.00 ns, 11.8141 ms/op
WorkloadResult  14: 64 op, 804277900.00 ns, 12.5668 ms/op
WorkloadResult  15: 64 op, 747446100.00 ns, 11.6788 ms/op
WorkloadResult  16: 64 op, 787233300.00 ns, 12.3005 ms/op
WorkloadResult  17: 64 op, 757076300.00 ns, 11.8293 ms/op
WorkloadResult  18: 64 op, 737604500.00 ns, 11.5251 ms/op
WorkloadResult  19: 64 op, 758248600.00 ns, 11.8476 ms/op
WorkloadResult  20: 64 op, 737257200.00 ns, 11.5196 ms/op
WorkloadResult  21: 64 op, 727538500.00 ns, 11.3678 ms/op
WorkloadResult  22: 64 op, 725999400.00 ns, 11.3437 ms/op
WorkloadResult  23: 64 op, 726194100.00 ns, 11.3468 ms/op
WorkloadResult  24: 64 op, 719804500.00 ns, 11.2469 ms/op
WorkloadResult  25: 64 op, 761419000.00 ns, 11.8972 ms/op
WorkloadResult  26: 64 op, 738636900.00 ns, 11.5412 ms/op
WorkloadResult  27: 64 op, 732264400.00 ns, 11.4416 ms/op
WorkloadResult  28: 64 op, 758948200.00 ns, 11.8586 ms/op
// GC:  31 31 31 1062455136 64
// Threading:  0 0 64

// AfterAll
// Benchmark Process 9200 has exited with code 0.

Mean = 11.776 ms, StdErr = 0.063 ms (0.54%), N = 28, StdDev = 0.334 ms
Min = 11.247 ms, Q1 = 11.524 ms, Median = 11.808 ms, Q3 = 11.970 ms, Max = 12.567 ms
IQR = 0.446 ms, LowerFence = 10.855 ms, UpperFence = 12.639 ms
ConfidenceInterval = [11.543 ms; 12.009 ms] (CI 99.9%), Margin = 0.233 ms (1.98% of Mean)
Skewness = 0.43, Kurtosis = 2.37, MValue = 2

// ** Remained 3 (30.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:55 (0h 2m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=100000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 875a7855-6805-431f-81e6-09e019e75bd1.dll --anonymousPipes 648 656 --benchmarkName "Sample.Benchmark.MessagePackVSJson.JsonSerializeAndDeserialize(Amount: 100000)" --job Default --benchmarkId 7 in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 181000.00 ns, 181.0000 us/op
WorkloadJitting  1: 1 op, 621505100.00 ns, 621.5051 ms/op

OverheadJitting  2: 1 op, 500.00 ns, 500.0000 ns/op
WorkloadJitting  2: 1 op, 153657400.00 ns, 153.6574 ms/op

WorkloadPilot    1: 2 op, 324775800.00 ns, 162.3879 ms/op
WorkloadPilot    2: 3 op, 486064600.00 ns, 162.0215 ms/op
WorkloadPilot    3: 4 op, 645991800.00 ns, 161.4980 ms/op

WorkloadWarmup   1: 4 op, 661356800.00 ns, 165.3392 ms/op
WorkloadWarmup   2: 4 op, 649527600.00 ns, 162.3819 ms/op
WorkloadWarmup   3: 4 op, 683241700.00 ns, 170.8104 ms/op
WorkloadWarmup   4: 4 op, 661673700.00 ns, 165.4184 ms/op
WorkloadWarmup   5: 4 op, 650572300.00 ns, 162.6431 ms/op
WorkloadWarmup   6: 4 op, 671827500.00 ns, 167.9569 ms/op
WorkloadWarmup   7: 4 op, 737464900.00 ns, 184.3662 ms/op
WorkloadWarmup   8: 4 op, 750353300.00 ns, 187.5883 ms/op
WorkloadWarmup   9: 4 op, 835497900.00 ns, 208.8745 ms/op
WorkloadWarmup  10: 4 op, 713483300.00 ns, 178.3708 ms/op

// BeforeActualRun
WorkloadActual   1: 4 op, 723062000.00 ns, 180.7655 ms/op
WorkloadActual   2: 4 op, 707792500.00 ns, 176.9481 ms/op
WorkloadActual   3: 4 op, 726310200.00 ns, 181.5776 ms/op
WorkloadActual   4: 4 op, 732924200.00 ns, 183.2311 ms/op
WorkloadActual   5: 4 op, 731046400.00 ns, 182.7616 ms/op
WorkloadActual   6: 4 op, 689459900.00 ns, 172.3650 ms/op
WorkloadActual   7: 4 op, 707747400.00 ns, 176.9369 ms/op
WorkloadActual   8: 4 op, 704797500.00 ns, 176.1994 ms/op
WorkloadActual   9: 4 op, 714805900.00 ns, 178.7015 ms/op
WorkloadActual  10: 4 op, 725232100.00 ns, 181.3080 ms/op
WorkloadActual  11: 4 op, 706248700.00 ns, 176.5622 ms/op
WorkloadActual  12: 4 op, 789704800.00 ns, 197.4262 ms/op
WorkloadActual  13: 4 op, 813177200.00 ns, 203.2943 ms/op
WorkloadActual  14: 4 op, 840171400.00 ns, 210.0429 ms/op
WorkloadActual  15: 4 op, 906901400.00 ns, 226.7254 ms/op
WorkloadActual  16: 4 op, 818242100.00 ns, 204.5605 ms/op
WorkloadActual  17: 4 op, 849178400.00 ns, 212.2946 ms/op
WorkloadActual  18: 4 op, 874178900.00 ns, 218.5447 ms/op
WorkloadActual  19: 4 op, 898794800.00 ns, 224.6987 ms/op
WorkloadActual  20: 4 op, 771323300.00 ns, 192.8308 ms/op
WorkloadActual  21: 4 op, 730757900.00 ns, 182.6895 ms/op
WorkloadActual  22: 4 op, 706897400.00 ns, 176.7244 ms/op
WorkloadActual  23: 4 op, 714983500.00 ns, 178.7459 ms/op
WorkloadActual  24: 4 op, 704132200.00 ns, 176.0331 ms/op
WorkloadActual  25: 4 op, 713393600.00 ns, 178.3484 ms/op
WorkloadActual  26: 4 op, 763467400.00 ns, 190.8669 ms/op
WorkloadActual  27: 4 op, 691946700.00 ns, 172.9867 ms/op
WorkloadActual  28: 4 op, 666594700.00 ns, 166.6487 ms/op
WorkloadActual  29: 4 op, 728395400.00 ns, 182.0989 ms/op
WorkloadActual  30: 4 op, 690074300.00 ns, 172.5186 ms/op
WorkloadActual  31: 4 op, 735074200.00 ns, 183.7686 ms/op
WorkloadActual  32: 4 op, 753681700.00 ns, 188.4204 ms/op
WorkloadActual  33: 4 op, 694790300.00 ns, 173.6976 ms/op
WorkloadActual  34: 4 op, 743206800.00 ns, 185.8017 ms/op
WorkloadActual  35: 4 op, 735918900.00 ns, 183.9797 ms/op
WorkloadActual  36: 4 op, 715463700.00 ns, 178.8659 ms/op
WorkloadActual  37: 4 op, 743501200.00 ns, 185.8753 ms/op
WorkloadActual  38: 4 op, 750277000.00 ns, 187.5693 ms/op
WorkloadActual  39: 4 op, 700013900.00 ns, 175.0035 ms/op
WorkloadActual  40: 4 op, 693074900.00 ns, 173.2687 ms/op
WorkloadActual  41: 4 op, 676557700.00 ns, 169.1394 ms/op
WorkloadActual  42: 4 op, 705439500.00 ns, 176.3599 ms/op
WorkloadActual  43: 4 op, 715891700.00 ns, 178.9729 ms/op
WorkloadActual  44: 4 op, 685911600.00 ns, 171.4779 ms/op
WorkloadActual  45: 4 op, 678955400.00 ns, 169.7389 ms/op
WorkloadActual  46: 4 op, 680833900.00 ns, 170.2085 ms/op
WorkloadActual  47: 4 op, 695054100.00 ns, 173.7635 ms/op
WorkloadActual  48: 4 op, 703224100.00 ns, 175.8060 ms/op
WorkloadActual  49: 4 op, 723702800.00 ns, 180.9257 ms/op
WorkloadActual  50: 4 op, 682608500.00 ns, 170.6521 ms/op
WorkloadActual  51: 4 op, 706235800.00 ns, 176.5590 ms/op

// AfterActualRun
WorkloadResult   1: 4 op, 723062000.00 ns, 180.7655 ms/op
WorkloadResult   2: 4 op, 707792500.00 ns, 176.9481 ms/op
WorkloadResult   3: 4 op, 726310200.00 ns, 181.5776 ms/op
WorkloadResult   4: 4 op, 732924200.00 ns, 183.2311 ms/op
WorkloadResult   5: 4 op, 731046400.00 ns, 182.7616 ms/op
WorkloadResult   6: 4 op, 689459900.00 ns, 172.3650 ms/op
WorkloadResult   7: 4 op, 707747400.00 ns, 176.9369 ms/op
WorkloadResult   8: 4 op, 704797500.00 ns, 176.1994 ms/op
WorkloadResult   9: 4 op, 714805900.00 ns, 178.7015 ms/op
WorkloadResult  10: 4 op, 725232100.00 ns, 181.3080 ms/op
WorkloadResult  11: 4 op, 706248700.00 ns, 176.5622 ms/op
WorkloadResult  12: 4 op, 789704800.00 ns, 197.4262 ms/op
WorkloadResult  13: 4 op, 771323300.00 ns, 192.8308 ms/op
WorkloadResult  14: 4 op, 730757900.00 ns, 182.6895 ms/op
WorkloadResult  15: 4 op, 706897400.00 ns, 176.7244 ms/op
WorkloadResult  16: 4 op, 714983500.00 ns, 178.7459 ms/op
WorkloadResult  17: 4 op, 704132200.00 ns, 176.0331 ms/op
WorkloadResult  18: 4 op, 713393600.00 ns, 178.3484 ms/op
WorkloadResult  19: 4 op, 763467400.00 ns, 190.8669 ms/op
WorkloadResult  20: 4 op, 691946700.00 ns, 172.9867 ms/op
WorkloadResult  21: 4 op, 666594700.00 ns, 166.6487 ms/op
WorkloadResult  22: 4 op, 728395400.00 ns, 182.0989 ms/op
WorkloadResult  23: 4 op, 690074300.00 ns, 172.5186 ms/op
WorkloadResult  24: 4 op, 735074200.00 ns, 183.7686 ms/op
WorkloadResult  25: 4 op, 753681700.00 ns, 188.4204 ms/op
WorkloadResult  26: 4 op, 694790300.00 ns, 173.6976 ms/op
WorkloadResult  27: 4 op, 743206800.00 ns, 185.8017 ms/op
WorkloadResult  28: 4 op, 735918900.00 ns, 183.9797 ms/op
WorkloadResult  29: 4 op, 715463700.00 ns, 178.8659 ms/op
WorkloadResult  30: 4 op, 743501200.00 ns, 185.8753 ms/op
WorkloadResult  31: 4 op, 750277000.00 ns, 187.5693 ms/op
WorkloadResult  32: 4 op, 700013900.00 ns, 175.0035 ms/op
WorkloadResult  33: 4 op, 693074900.00 ns, 173.2687 ms/op
WorkloadResult  34: 4 op, 676557700.00 ns, 169.1394 ms/op
WorkloadResult  35: 4 op, 705439500.00 ns, 176.3599 ms/op
WorkloadResult  36: 4 op, 715891700.00 ns, 178.9729 ms/op
WorkloadResult  37: 4 op, 685911600.00 ns, 171.4779 ms/op
WorkloadResult  38: 4 op, 678955400.00 ns, 169.7389 ms/op
WorkloadResult  39: 4 op, 680833900.00 ns, 170.2085 ms/op
WorkloadResult  40: 4 op, 695054100.00 ns, 173.7635 ms/op
WorkloadResult  41: 4 op, 703224100.00 ns, 175.8060 ms/op
WorkloadResult  42: 4 op, 723702800.00 ns, 180.9257 ms/op
WorkloadResult  43: 4 op, 682608500.00 ns, 170.6521 ms/op
WorkloadResult  44: 4 op, 706235800.00 ns, 176.5590 ms/op
// GC:  32 17 5 346522648 4
// Threading:  0 0 4

// AfterAll
// Benchmark Process 4320 has exited with code 0.

Mean = 178.753 ms, StdErr = 0.995 ms (0.56%), N = 44, StdDev = 6.598 ms
Min = 166.649 ms, Q1 = 173.747 ms, Median = 177.648 ms, Q3 = 182.708 ms, Max = 197.426 ms
IQR = 8.960 ms, LowerFence = 160.306 ms, UpperFence = 196.148 ms
ConfidenceInterval = [175.240 ms; 182.266 ms] (CI 99.9%), Margin = 3.513 ms (1.97% of Mean)
Skewness = 0.61, Kurtosis = 3.13, MValue = 2

// ** Remained 2 (20.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:55 (0h 1m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=100000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 875a7855-6805-431f-81e6-09e019e75bd1.dll --anonymousPipes 1552 1412 --benchmarkName "Sample.Benchmark.MessagePackVSJson.MpackSerializeAndDeserialize(Amount: 100000)" --job Default --benchmarkId 8 in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 172400.00 ns, 172.4000 us/op
WorkloadJitting  1: 1 op, 264346000.00 ns, 264.3460 ms/op

WorkloadPilot    1: 2 op, 203657400.00 ns, 101.8287 ms/op
WorkloadPilot    2: 3 op, 75629100.00 ns, 25.2097 ms/op
WorkloadPilot    3: 4 op, 100626400.00 ns, 25.1566 ms/op
WorkloadPilot    4: 5 op, 126185300.00 ns, 25.2371 ms/op
WorkloadPilot    5: 6 op, 151165500.00 ns, 25.1943 ms/op
WorkloadPilot    6: 7 op, 229660500.00 ns, 32.8086 ms/op
WorkloadPilot    7: 8 op, 232509600.00 ns, 29.0637 ms/op
WorkloadPilot    8: 9 op, 234890000.00 ns, 26.0989 ms/op
WorkloadPilot    9: 10 op, 245908000.00 ns, 24.5908 ms/op
WorkloadPilot   10: 11 op, 273628500.00 ns, 24.8753 ms/op
WorkloadPilot   11: 12 op, 307720300.00 ns, 25.6434 ms/op
WorkloadPilot   12: 13 op, 335338100.00 ns, 25.7952 ms/op
WorkloadPilot   13: 14 op, 360955700.00 ns, 25.7826 ms/op
WorkloadPilot   14: 15 op, 379221200.00 ns, 25.2814 ms/op
WorkloadPilot   15: 16 op, 441877200.00 ns, 27.6173 ms/op
WorkloadPilot   16: 32 op, 814091300.00 ns, 25.4404 ms/op

WorkloadWarmup   1: 32 op, 815505400.00 ns, 25.4845 ms/op
WorkloadWarmup   2: 32 op, 804402500.00 ns, 25.1376 ms/op
WorkloadWarmup   3: 32 op, 833883000.00 ns, 26.0588 ms/op
WorkloadWarmup   4: 32 op, 818718700.00 ns, 25.5850 ms/op
WorkloadWarmup   5: 32 op, 786217300.00 ns, 24.5693 ms/op
WorkloadWarmup   6: 32 op, 778535100.00 ns, 24.3292 ms/op
WorkloadWarmup   7: 32 op, 772749700.00 ns, 24.1484 ms/op
WorkloadWarmup   8: 32 op, 794148200.00 ns, 24.8171 ms/op
WorkloadWarmup   9: 32 op, 780232200.00 ns, 24.3823 ms/op

// BeforeActualRun
WorkloadActual   1: 32 op, 834131600.00 ns, 26.0666 ms/op
WorkloadActual   2: 32 op, 789381800.00 ns, 24.6682 ms/op
WorkloadActual   3: 32 op, 765624900.00 ns, 23.9258 ms/op
WorkloadActual   4: 32 op, 763466700.00 ns, 23.8583 ms/op
WorkloadActual   5: 32 op, 769632700.00 ns, 24.0510 ms/op
WorkloadActual   6: 32 op, 766337100.00 ns, 23.9480 ms/op
WorkloadActual   7: 32 op, 767274200.00 ns, 23.9773 ms/op
WorkloadActual   8: 32 op, 811233100.00 ns, 25.3510 ms/op
WorkloadActual   9: 32 op, 790728100.00 ns, 24.7103 ms/op
WorkloadActual  10: 32 op, 810584400.00 ns, 25.3308 ms/op
WorkloadActual  11: 32 op, 808517500.00 ns, 25.2662 ms/op
WorkloadActual  12: 32 op, 779246200.00 ns, 24.3514 ms/op
WorkloadActual  13: 32 op, 782449600.00 ns, 24.4516 ms/op
WorkloadActual  14: 32 op, 852170000.00 ns, 26.6303 ms/op
WorkloadActual  15: 32 op, 776747500.00 ns, 24.2734 ms/op
WorkloadActual  16: 32 op, 773641600.00 ns, 24.1763 ms/op
WorkloadActual  17: 32 op, 782657000.00 ns, 24.4580 ms/op
WorkloadActual  18: 32 op, 812564800.00 ns, 25.3927 ms/op
WorkloadActual  19: 32 op, 816996400.00 ns, 25.5311 ms/op
WorkloadActual  20: 32 op, 784027600.00 ns, 24.5009 ms/op
WorkloadActual  21: 32 op, 891194900.00 ns, 27.8498 ms/op
WorkloadActual  22: 32 op, 846492800.00 ns, 26.4529 ms/op
WorkloadActual  23: 32 op, 809816400.00 ns, 25.3068 ms/op
WorkloadActual  24: 32 op, 797659600.00 ns, 24.9269 ms/op
WorkloadActual  25: 32 op, 785981900.00 ns, 24.5619 ms/op
WorkloadActual  26: 32 op, 811813400.00 ns, 25.3692 ms/op
WorkloadActual  27: 32 op, 866763000.00 ns, 27.0863 ms/op
WorkloadActual  28: 32 op, 842734900.00 ns, 26.3355 ms/op
WorkloadActual  29: 32 op, 842041400.00 ns, 26.3138 ms/op
WorkloadActual  30: 32 op, 777044200.00 ns, 24.2826 ms/op
WorkloadActual  31: 32 op, 791475600.00 ns, 24.7336 ms/op
WorkloadActual  32: 32 op, 775628100.00 ns, 24.2384 ms/op
WorkloadActual  33: 32 op, 786073600.00 ns, 24.5648 ms/op
WorkloadActual  34: 32 op, 917413700.00 ns, 28.6692 ms/op
WorkloadActual  35: 32 op, 888944700.00 ns, 27.7795 ms/op
WorkloadActual  36: 32 op, 893759600.00 ns, 27.9300 ms/op
WorkloadActual  37: 32 op, 939008900.00 ns, 29.3440 ms/op
WorkloadActual  38: 32 op, 862021100.00 ns, 26.9382 ms/op
WorkloadActual  39: 32 op, 847476400.00 ns, 26.4836 ms/op
WorkloadActual  40: 32 op, 914597100.00 ns, 28.5812 ms/op
WorkloadActual  41: 32 op, 856552300.00 ns, 26.7673 ms/op
WorkloadActual  42: 32 op, 904357400.00 ns, 28.2612 ms/op
WorkloadActual  43: 32 op, 895300700.00 ns, 27.9781 ms/op
WorkloadActual  44: 32 op, 846966500.00 ns, 26.4677 ms/op
WorkloadActual  45: 32 op, 859644600.00 ns, 26.8639 ms/op
WorkloadActual  46: 32 op, 864384600.00 ns, 27.0120 ms/op
WorkloadActual  47: 32 op, 927811500.00 ns, 28.9941 ms/op
WorkloadActual  48: 32 op, 871052800.00 ns, 27.2204 ms/op
WorkloadActual  49: 32 op, 877886800.00 ns, 27.4340 ms/op
WorkloadActual  50: 32 op, 897585000.00 ns, 28.0495 ms/op
WorkloadActual  51: 32 op, 933394300.00 ns, 29.1686 ms/op
WorkloadActual  52: 32 op, 877136100.00 ns, 27.4105 ms/op
WorkloadActual  53: 32 op, 1026602000.00 ns, 32.0813 ms/op
WorkloadActual  54: 32 op, 887305500.00 ns, 27.7283 ms/op
WorkloadActual  55: 32 op, 858491000.00 ns, 26.8278 ms/op
WorkloadActual  56: 32 op, 898097700.00 ns, 28.0656 ms/op
WorkloadActual  57: 32 op, 838392800.00 ns, 26.1998 ms/op
WorkloadActual  58: 32 op, 851497600.00 ns, 26.6093 ms/op
WorkloadActual  59: 32 op, 1006144500.00 ns, 31.4420 ms/op
WorkloadActual  60: 32 op, 971755800.00 ns, 30.3674 ms/op
WorkloadActual  61: 32 op, 867165600.00 ns, 27.0989 ms/op
WorkloadActual  62: 32 op, 859921600.00 ns, 26.8726 ms/op
WorkloadActual  63: 32 op, 837915600.00 ns, 26.1849 ms/op
WorkloadActual  64: 32 op, 889354700.00 ns, 27.7923 ms/op
WorkloadActual  65: 32 op, 969884900.00 ns, 30.3089 ms/op
WorkloadActual  66: 32 op, 859197200.00 ns, 26.8499 ms/op
WorkloadActual  67: 32 op, 904244900.00 ns, 28.2577 ms/op
WorkloadActual  68: 32 op, 867112500.00 ns, 27.0973 ms/op
WorkloadActual  69: 32 op, 850363300.00 ns, 26.5739 ms/op
WorkloadActual  70: 32 op, 897993400.00 ns, 28.0623 ms/op
WorkloadActual  71: 32 op, 795077200.00 ns, 24.8462 ms/op
WorkloadActual  72: 32 op, 804570500.00 ns, 25.1428 ms/op
WorkloadActual  73: 32 op, 801807200.00 ns, 25.0565 ms/op
WorkloadActual  74: 32 op, 812865300.00 ns, 25.4020 ms/op
WorkloadActual  75: 32 op, 821168900.00 ns, 25.6615 ms/op
WorkloadActual  76: 32 op, 804610200.00 ns, 25.1441 ms/op
WorkloadActual  77: 32 op, 785471700.00 ns, 24.5460 ms/op
WorkloadActual  78: 32 op, 841456100.00 ns, 26.2955 ms/op
WorkloadActual  79: 32 op, 971501000.00 ns, 30.3594 ms/op
WorkloadActual  80: 32 op, 1053549800.00 ns, 32.9234 ms/op
WorkloadActual  81: 32 op, 846897300.00 ns, 26.4655 ms/op
WorkloadActual  82: 32 op, 854070500.00 ns, 26.6897 ms/op
WorkloadActual  83: 32 op, 1012730400.00 ns, 31.6478 ms/op
WorkloadActual  84: 32 op, 898154600.00 ns, 28.0673 ms/op
WorkloadActual  85: 32 op, 897151300.00 ns, 28.0360 ms/op
WorkloadActual  86: 32 op, 969372000.00 ns, 30.2929 ms/op
WorkloadActual  87: 32 op, 884742500.00 ns, 27.6482 ms/op
WorkloadActual  88: 32 op, 884526900.00 ns, 27.6415 ms/op
WorkloadActual  89: 32 op, 805439400.00 ns, 25.1700 ms/op
WorkloadActual  90: 32 op, 805487300.00 ns, 25.1715 ms/op
WorkloadActual  91: 32 op, 795719100.00 ns, 24.8662 ms/op
WorkloadActual  92: 32 op, 800480800.00 ns, 25.0150 ms/op
WorkloadActual  93: 32 op, 809503100.00 ns, 25.2970 ms/op
WorkloadActual  94: 32 op, 847451000.00 ns, 26.4828 ms/op
WorkloadActual  95: 32 op, 836815200.00 ns, 26.1505 ms/op
WorkloadActual  96: 32 op, 855328800.00 ns, 26.7290 ms/op
WorkloadActual  97: 32 op, 799080400.00 ns, 24.9713 ms/op
WorkloadActual  98: 32 op, 801172800.00 ns, 25.0367 ms/op
WorkloadActual  99: 32 op, 833614500.00 ns, 26.0505 ms/op
WorkloadActual  100: 32 op, 786649900.00 ns, 24.5828 ms/op

// AfterActualRun
WorkloadResult   1: 32 op, 834131600.00 ns, 26.0666 ms/op
WorkloadResult   2: 32 op, 789381800.00 ns, 24.6682 ms/op
WorkloadResult   3: 32 op, 765624900.00 ns, 23.9258 ms/op
WorkloadResult   4: 32 op, 763466700.00 ns, 23.8583 ms/op
WorkloadResult   5: 32 op, 769632700.00 ns, 24.0510 ms/op
WorkloadResult   6: 32 op, 766337100.00 ns, 23.9480 ms/op
WorkloadResult   7: 32 op, 767274200.00 ns, 23.9773 ms/op
WorkloadResult   8: 32 op, 811233100.00 ns, 25.3510 ms/op
WorkloadResult   9: 32 op, 790728100.00 ns, 24.7103 ms/op
WorkloadResult  10: 32 op, 810584400.00 ns, 25.3308 ms/op
WorkloadResult  11: 32 op, 808517500.00 ns, 25.2662 ms/op
WorkloadResult  12: 32 op, 779246200.00 ns, 24.3514 ms/op
WorkloadResult  13: 32 op, 782449600.00 ns, 24.4516 ms/op
WorkloadResult  14: 32 op, 852170000.00 ns, 26.6303 ms/op
WorkloadResult  15: 32 op, 776747500.00 ns, 24.2734 ms/op
WorkloadResult  16: 32 op, 773641600.00 ns, 24.1763 ms/op
WorkloadResult  17: 32 op, 782657000.00 ns, 24.4580 ms/op
WorkloadResult  18: 32 op, 812564800.00 ns, 25.3927 ms/op
WorkloadResult  19: 32 op, 816996400.00 ns, 25.5311 ms/op
WorkloadResult  20: 32 op, 784027600.00 ns, 24.5009 ms/op
WorkloadResult  21: 32 op, 891194900.00 ns, 27.8498 ms/op
WorkloadResult  22: 32 op, 846492800.00 ns, 26.4529 ms/op
WorkloadResult  23: 32 op, 809816400.00 ns, 25.3068 ms/op
WorkloadResult  24: 32 op, 797659600.00 ns, 24.9269 ms/op
WorkloadResult  25: 32 op, 785981900.00 ns, 24.5619 ms/op
WorkloadResult  26: 32 op, 811813400.00 ns, 25.3692 ms/op
WorkloadResult  27: 32 op, 866763000.00 ns, 27.0863 ms/op
WorkloadResult  28: 32 op, 842734900.00 ns, 26.3355 ms/op
WorkloadResult  29: 32 op, 842041400.00 ns, 26.3138 ms/op
WorkloadResult  30: 32 op, 777044200.00 ns, 24.2826 ms/op
WorkloadResult  31: 32 op, 791475600.00 ns, 24.7336 ms/op
WorkloadResult  32: 32 op, 775628100.00 ns, 24.2384 ms/op
WorkloadResult  33: 32 op, 786073600.00 ns, 24.5648 ms/op
WorkloadResult  34: 32 op, 917413700.00 ns, 28.6692 ms/op
WorkloadResult  35: 32 op, 888944700.00 ns, 27.7795 ms/op
WorkloadResult  36: 32 op, 893759600.00 ns, 27.9300 ms/op
WorkloadResult  37: 32 op, 939008900.00 ns, 29.3440 ms/op
WorkloadResult  38: 32 op, 862021100.00 ns, 26.9382 ms/op
WorkloadResult  39: 32 op, 847476400.00 ns, 26.4836 ms/op
WorkloadResult  40: 32 op, 914597100.00 ns, 28.5812 ms/op
WorkloadResult  41: 32 op, 856552300.00 ns, 26.7673 ms/op
WorkloadResult  42: 32 op, 904357400.00 ns, 28.2612 ms/op
WorkloadResult  43: 32 op, 895300700.00 ns, 27.9781 ms/op
WorkloadResult  44: 32 op, 846966500.00 ns, 26.4677 ms/op
WorkloadResult  45: 32 op, 859644600.00 ns, 26.8639 ms/op
WorkloadResult  46: 32 op, 864384600.00 ns, 27.0120 ms/op
WorkloadResult  47: 32 op, 927811500.00 ns, 28.9941 ms/op
WorkloadResult  48: 32 op, 871052800.00 ns, 27.2204 ms/op
WorkloadResult  49: 32 op, 877886800.00 ns, 27.4340 ms/op
WorkloadResult  50: 32 op, 897585000.00 ns, 28.0495 ms/op
WorkloadResult  51: 32 op, 933394300.00 ns, 29.1686 ms/op
WorkloadResult  52: 32 op, 877136100.00 ns, 27.4105 ms/op
WorkloadResult  53: 32 op, 887305500.00 ns, 27.7283 ms/op
WorkloadResult  54: 32 op, 858491000.00 ns, 26.8278 ms/op
WorkloadResult  55: 32 op, 898097700.00 ns, 28.0656 ms/op
WorkloadResult  56: 32 op, 838392800.00 ns, 26.1998 ms/op
WorkloadResult  57: 32 op, 851497600.00 ns, 26.6093 ms/op
WorkloadResult  58: 32 op, 1006144500.00 ns, 31.4420 ms/op
WorkloadResult  59: 32 op, 971755800.00 ns, 30.3674 ms/op
WorkloadResult  60: 32 op, 867165600.00 ns, 27.0989 ms/op
WorkloadResult  61: 32 op, 859921600.00 ns, 26.8726 ms/op
WorkloadResult  62: 32 op, 837915600.00 ns, 26.1849 ms/op
WorkloadResult  63: 32 op, 889354700.00 ns, 27.7923 ms/op
WorkloadResult  64: 32 op, 969884900.00 ns, 30.3089 ms/op
WorkloadResult  65: 32 op, 859197200.00 ns, 26.8499 ms/op
WorkloadResult  66: 32 op, 904244900.00 ns, 28.2577 ms/op
WorkloadResult  67: 32 op, 867112500.00 ns, 27.0973 ms/op
WorkloadResult  68: 32 op, 850363300.00 ns, 26.5739 ms/op
WorkloadResult  69: 32 op, 897993400.00 ns, 28.0623 ms/op
WorkloadResult  70: 32 op, 795077200.00 ns, 24.8462 ms/op
WorkloadResult  71: 32 op, 804570500.00 ns, 25.1428 ms/op
WorkloadResult  72: 32 op, 801807200.00 ns, 25.0565 ms/op
WorkloadResult  73: 32 op, 812865300.00 ns, 25.4020 ms/op
WorkloadResult  74: 32 op, 821168900.00 ns, 25.6615 ms/op
WorkloadResult  75: 32 op, 804610200.00 ns, 25.1441 ms/op
WorkloadResult  76: 32 op, 785471700.00 ns, 24.5460 ms/op
WorkloadResult  77: 32 op, 841456100.00 ns, 26.2955 ms/op
WorkloadResult  78: 32 op, 971501000.00 ns, 30.3594 ms/op
WorkloadResult  79: 32 op, 846897300.00 ns, 26.4655 ms/op
WorkloadResult  80: 32 op, 854070500.00 ns, 26.6897 ms/op
WorkloadResult  81: 32 op, 1012730400.00 ns, 31.6478 ms/op
WorkloadResult  82: 32 op, 898154600.00 ns, 28.0673 ms/op
WorkloadResult  83: 32 op, 897151300.00 ns, 28.0360 ms/op
WorkloadResult  84: 32 op, 969372000.00 ns, 30.2929 ms/op
WorkloadResult  85: 32 op, 884742500.00 ns, 27.6482 ms/op
WorkloadResult  86: 32 op, 884526900.00 ns, 27.6415 ms/op
WorkloadResult  87: 32 op, 805439400.00 ns, 25.1700 ms/op
WorkloadResult  88: 32 op, 805487300.00 ns, 25.1715 ms/op
WorkloadResult  89: 32 op, 795719100.00 ns, 24.8662 ms/op
WorkloadResult  90: 32 op, 800480800.00 ns, 25.0150 ms/op
WorkloadResult  91: 32 op, 809503100.00 ns, 25.2970 ms/op
WorkloadResult  92: 32 op, 847451000.00 ns, 26.4828 ms/op
WorkloadResult  93: 32 op, 836815200.00 ns, 26.1505 ms/op
WorkloadResult  94: 32 op, 855328800.00 ns, 26.7290 ms/op
WorkloadResult  95: 32 op, 799080400.00 ns, 24.9713 ms/op
WorkloadResult  96: 32 op, 801172800.00 ns, 25.0367 ms/op
WorkloadResult  97: 32 op, 833614500.00 ns, 26.0505 ms/op
WorkloadResult  98: 32 op, 786649900.00 ns, 24.5828 ms/op
// GC:  47 45 29 710453680 32
// Threading:  0 0 32

// AfterAll
// Benchmark Process 25040 has exited with code 0.

Mean = 26.440 ms, StdErr = 0.180 ms (0.68%), N = 98, StdDev = 1.781 ms
Min = 23.858 ms, Q1 = 25.020 ms, Median = 26.394 ms, Q3 = 27.647 ms, Max = 31.648 ms
IQR = 2.626 ms, LowerFence = 21.081 ms, UpperFence = 31.586 ms
ConfidenceInterval = [25.829 ms; 27.050 ms] (CI 99.9%), Margin = 0.610 ms (2.31% of Mean)
Skewness = 0.73, Kurtosis = 3.11, MValue = 3.52

// ** Remained 1 (10.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:56 (0h 0m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.MpackSerializeAndDeserializeV2: DefaultJob [Amount=100000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 875a7855-6805-431f-81e6-09e019e75bd1.dll --anonymousPipes 1604 1600 --benchmarkName "Sample.Benchmark.MessagePackVSJson.MpackSerializeAndDeserializeV2(Amount: 100000)" --job Default --benchmarkId 9 in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 166900.00 ns, 166.9000 us/op

System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.
 ---> MessagePack.MessagePackSerializationException: Failed to serialize System.Collections.Generic.List`1[[Sample.Benchmark.MessageItem, Sample.Benchmark, Version=*******, Culture=neutral, PublicKeyToken=null]] value.
 ---> MessagePack.FormatterNotRegisteredException: Sample.Benchmark.MessageItem is not registered in resolver: MessagePack.Resolvers.StandardResolver
   at MessagePack.FormatterResolverExtensions.Throw(Type t, IFormatterResolver resolver)
   at MessagePack.Formatters.ListFormatter`1.Serialize(MessagePackWriter& writer, List`1 value, MessagePackSerializerOptions options)
   at MessagePack.MessagePackSerializer.Serialize[T](MessagePackWriter& writer, T value, MessagePackSerializerOptions options)
   --- End of inner exception stack trace ---
   at MessagePack.MessagePackSerializer.Serialize[T](MessagePackWriter& writer, T value, MessagePackSerializerOptions options)
   at MessagePack.MessagePackSerializer.Serialize[T](T value, MessagePackSerializerOptions options, CancellationToken cancellationToken)
   at Sample.Benchmark.MessagePackVSJson.MpackSerializeAndDeserializeV2() in D:\代码\Sample\Sample.Benchmark\MessagePackVSJson.cs:line 76
   at BenchmarkDotNet.Autogenerated.Runnable_9.WorkloadActionNoUnroll(Int64 invokeCount) in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\875a7855-6805-431f-81e6-09e019e75bd1.notcs:line 2039
   at BenchmarkDotNet.Engines.Engine.Measure(Action`1 action, Int64 invokeCount)
   at BenchmarkDotNet.Engines.Engine.RunIteration(IterationData data)
   at BenchmarkDotNet.Engines.EngineFactory.Jit(Engine engine, Int32 jitIndex, Int32 invokeCount, Int32 unrollFactor)
   at BenchmarkDotNet.Engines.EngineFactory.CreateReadyToRun(EngineParameters engineParameters)
   at BenchmarkDotNet.Autogenerated.Runnable_9.Run(IHost host, String benchmarkName) in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\875a7855-6805-431f-81e6-09e019e75bd1.notcs:line 1904
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   --- End of inner exception stack trace ---
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.MethodBase.Invoke(Object obj, Object[] parameters)
   at BenchmarkDotNet.Autogenerated.UniqueProgramName.AfterAssemblyLoadingAttached(String[] args) in D:\代码\Sample\Sample.Benchmark\bin\Release\net9.0\875a7855-6805-431f-81e6-09e019e75bd1\875a7855-6805-431f-81e6-09e019e75bd1.notcs:line 57
// AfterAll
No Workload Results were obtained from the run.
// Benchmark Process 21852 has exited with code -1.

// ** Remained 0 (0.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:55 (0h 0m from now) **
Successfully reverted power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// ***** BenchmarkRunner: Finish  *****

// * Export *
  BenchmarkDotNet.Artifacts\results\Sample.Benchmark.MessagePackVSJson-report.csv
  BenchmarkDotNet.Artifacts\results\Sample.Benchmark.MessagePackVSJson-report-github.md
  BenchmarkDotNet.Artifacts\results\Sample.Benchmark.MessagePackVSJson-report.html
  BenchmarkDotNet.Artifacts\results\Sample.Benchmark.MessagePackVSJson-report-default.md

// * Detailed results *
MessagePackVSJson.JsonSerialize: DefaultJob [Amount=10000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 5.083 ms, StdErr = 0.019 ms (0.37%), N = 14, StdDev = 0.071 ms
Min = 4.979 ms, Q1 = 5.044 ms, Median = 5.074 ms, Q3 = 5.108 ms, Max = 5.226 ms
IQR = 0.064 ms, LowerFence = 4.949 ms, UpperFence = 5.204 ms
ConfidenceInterval = [5.003 ms; 5.163 ms] (CI 99.9%), Margin = 0.080 ms (1.57% of Mean)
Skewness = 0.42, Kurtosis = 2.11, MValue = 2
-------------------- Histogram --------------------
[4.940 ms ; 5.127 ms) | @@@@@@@@@@@
[5.127 ms ; 5.246 ms) | @@@
---------------------------------------------------

MessagePackVSJson.MpackSerialize: DefaultJob [Amount=10000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 1.346 ms, StdErr = 0.015 ms (1.11%), N = 96, StdDev = 0.146 ms
Min = 1.187 ms, Q1 = 1.238 ms, Median = 1.274 ms, Q3 = 1.435 ms, Max = 1.765 ms
IQR = 0.197 ms, LowerFence = 0.943 ms, UpperFence = 1.731 ms
ConfidenceInterval = [1.295 ms; 1.396 ms] (CI 99.9%), Margin = 0.051 ms (3.76% of Mean)
Skewness = 1.05, Kurtosis = 3.06, MValue = 2.44
-------------------- Histogram --------------------
[1.145 ms ; 1.200 ms) | @@@@
[1.200 ms ; 1.284 ms) | @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
[1.284 ms ; 1.353 ms) | @@@@@@@
[1.353 ms ; 1.437 ms) | @@@@@@@@@@@@@@
[1.437 ms ; 1.479 ms) | @@@
[1.479 ms ; 1.562 ms) | @@@@@@@@@
[1.562 ms ; 1.657 ms) | @@@@@@@@
[1.657 ms ; 1.723 ms) | @
[1.723 ms ; 1.807 ms) | @@
---------------------------------------------------

MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=10000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 17.712 ms, StdErr = 0.152 ms (0.86%), N = 98, StdDev = 1.508 ms
Min = 15.300 ms, Q1 = 16.566 ms, Median = 17.243 ms, Q3 = 18.466 ms, Max = 21.380 ms
IQR = 1.900 ms, LowerFence = 13.716 ms, UpperFence = 21.317 ms
ConfidenceInterval = [17.195 ms; 18.229 ms] (CI 99.9%), Margin = 0.517 ms (2.92% of Mean)
Skewness = 0.85, Kurtosis = 2.71, MValue = 2.33
-------------------- Histogram --------------------
[14.871 ms ; 15.554 ms) | @
[15.554 ms ; 16.318 ms) | @@@@@@@@@@
[16.318 ms ; 17.177 ms) | @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
[17.177 ms ; 18.064 ms) | @@@@@@@@@@@@@@@@@@@
[18.064 ms ; 19.055 ms) | @@@@@@@@@@@
[19.055 ms ; 19.455 ms) | @@@
[19.455 ms ; 20.314 ms) | @@@@@@@@@
[20.314 ms ; 21.423 ms) | @@@@@@@@
---------------------------------------------------

MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=10000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 2.907 ms, StdErr = 0.046 ms (1.59%), N = 96, StdDev = 0.453 ms
Min = 2.380 ms, Q1 = 2.559 ms, Median = 2.787 ms, Q3 = 3.143 ms, Max = 4.203 ms
IQR = 0.584 ms, LowerFence = 1.683 ms, UpperFence = 4.019 ms
ConfidenceInterval = [2.749 ms; 3.064 ms] (CI 99.9%), Margin = 0.157 ms (5.41% of Mean)
Skewness = 1.08, Kurtosis = 3.25, MValue = 2.45
-------------------- Histogram --------------------
[2.250 ms ; 2.403 ms) | @
[2.403 ms ; 2.663 ms) | @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
[2.663 ms ; 2.953 ms) | @@@@@@@@@@@@@@@@@@@@@@@@@@@
[2.953 ms ; 3.261 ms) | @@@@@@@@@@@@
[3.261 ms ; 3.429 ms) | @
[3.429 ms ; 3.688 ms) | @@@@@@@@@
[3.688 ms ; 3.995 ms) | @@@@@@
[3.995 ms ; 4.333 ms) | @@
---------------------------------------------------

MessagePackVSJson.MpackSerializeAndDeserializeV2: DefaultJob [Amount=10000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
There are not any results runs

MessagePackVSJson.JsonSerialize: DefaultJob [Amount=100000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 69.560 ms, StdErr = 0.593 ms (0.85%), N = 92, StdDev = 5.686 ms
Min = 59.300 ms, Q1 = 65.655 ms, Median = 69.169 ms, Q3 = 71.828 ms, Max = 84.728 ms
IQR = 6.173 ms, LowerFence = 56.395 ms, UpperFence = 81.088 ms
ConfidenceInterval = [67.544 ms; 71.576 ms] (CI 99.9%), Margin = 2.016 ms (2.90% of Mean)
Skewness = 0.71, Kurtosis = 3.3, MValue = 2.36
-------------------- Histogram --------------------
[57.647 ms ; 61.392 ms) | @@@@
[61.392 ms ; 64.963 ms) | @@@@@@@@@@@@@@@
[64.963 ms ; 68.593 ms) | @@@@@@@@@@@@@@@@@@@@@
[68.593 ms ; 71.900 ms) | @@@@@@@@@@@@@@@@@@@@@@@@@@@@@
[71.900 ms ; 75.448 ms) | @@@@@@@@@@@
[75.448 ms ; 79.838 ms) | @@@@@@
[79.838 ms ; 81.586 ms) | @
[81.586 ms ; 84.893 ms) | @@@@@
---------------------------------------------------

MessagePackVSJson.MpackSerialize: DefaultJob [Amount=100000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 11.776 ms, StdErr = 0.063 ms (0.54%), N = 28, StdDev = 0.334 ms
Min = 11.247 ms, Q1 = 11.524 ms, Median = 11.808 ms, Q3 = 11.970 ms, Max = 12.567 ms
IQR = 0.446 ms, LowerFence = 10.855 ms, UpperFence = 12.639 ms
ConfidenceInterval = [11.543 ms; 12.009 ms] (CI 99.9%), Margin = 0.233 ms (1.98% of Mean)
Skewness = 0.43, Kurtosis = 2.37, MValue = 2
-------------------- Histogram --------------------
[11.103 ms ; 11.341 ms) | @
[11.341 ms ; 11.630 ms) | @@@@@@@@@@
[11.630 ms ; 12.071 ms) | @@@@@@@@@@@@
[12.071 ms ; 12.374 ms) | @@@@
[12.374 ms ; 12.711 ms) | @
---------------------------------------------------

MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=100000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 178.753 ms, StdErr = 0.995 ms (0.56%), N = 44, StdDev = 6.598 ms
Min = 166.649 ms, Q1 = 173.747 ms, Median = 177.648 ms, Q3 = 182.708 ms, Max = 197.426 ms
IQR = 8.960 ms, LowerFence = 160.306 ms, UpperFence = 196.148 ms
ConfidenceInterval = [175.240 ms; 182.266 ms] (CI 99.9%), Margin = 3.513 ms (1.97% of Mean)
Skewness = 0.61, Kurtosis = 3.13, MValue = 2
-------------------- Histogram --------------------
[166.610 ms ; 172.204 ms) | @@@@@@
[172.204 ms ; 177.109 ms) | @@@@@@@@@@@@@@@@
[177.109 ms ; 183.243 ms) | @@@@@@@@@@@@@
[183.243 ms ; 188.547 ms) | @@@@@@
[188.547 ms ; 194.302 ms) | @@
[194.302 ms ; 199.879 ms) | @
---------------------------------------------------

MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=100000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 26.440 ms, StdErr = 0.180 ms (0.68%), N = 98, StdDev = 1.781 ms
Min = 23.858 ms, Q1 = 25.020 ms, Median = 26.394 ms, Q3 = 27.647 ms, Max = 31.648 ms
IQR = 2.626 ms, LowerFence = 21.081 ms, UpperFence = 31.586 ms
ConfidenceInterval = [25.829 ms; 27.050 ms] (CI 99.9%), Margin = 0.610 ms (2.31% of Mean)
Skewness = 0.73, Kurtosis = 3.11, MValue = 3.52
-------------------- Histogram --------------------
[23.351 ms ; 24.420 ms) | @@@@@@@@@@
[24.420 ms ; 25.434 ms) | @@@@@@@@@@@@@@@@@@@@@@@@@@@@@
[25.434 ms ; 26.118 ms) | @@@@
[26.118 ms ; 27.132 ms) | @@@@@@@@@@@@@@@@@@@@@@@@@@
[27.132 ms ; 28.343 ms) | @@@@@@@@@@@@@@@@@@
[28.343 ms ; 29.470 ms) | @@@@@
[29.470 ms ; 30.837 ms) | @@@@
[30.837 ms ; 32.155 ms) | @@
---------------------------------------------------

MessagePackVSJson.MpackSerializeAndDeserializeV2: DefaultJob [Amount=100000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
There are not any results runs

// * Summary *

BenchmarkDotNet v0.14.0, Windows 11 (10.0.27764.1000)
AMD Ryzen 7 5800H with Radeon Graphics, 1 CPU, 16 logical and 8 physical cores
.NET SDK 9.0.100
  [Host]     : .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
  DefaultJob : .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2


| Method                         | Amount | Mean       | Error     | StdDev    | Median     | Gen0      | Gen1      | Gen2      | Allocated |
|------------------------------- |------- |-----------:|----------:|----------:|-----------:|----------:|----------:|----------:|----------:|
| JsonSerialize                  | 10000  |   5.083 ms | 0.0797 ms | 0.0707 ms |   5.074 ms |  898.4375 |  820.3125 |  437.5000 |   6.24 MB |
| MpackSerialize                 | 10000  |   1.346 ms | 0.0506 ms | 0.1459 ms |   1.274 ms |  201.1719 |  201.1719 |  201.1719 |   1.58 MB |
| JsonSerializeAndDeserialize    | 10000  |  17.712 ms | 0.5170 ms | 1.5082 ms |  17.243 ms | 1187.5000 | 1000.0000 |  500.0000 |   8.33 MB |
| MpackSerializeAndDeserialize   | 10000  |   2.907 ms | 0.1571 ms | 0.4533 ms |   2.787 ms |  324.2188 |  289.0625 |  257.8125 |   2.12 MB |
| MpackSerializeAndDeserializeV2 | 10000  |         NA |        NA |        NA |         NA |        NA |        NA |        NA |        NA |
| JsonSerialize                  | 100000 |  69.560 ms | 2.0161 ms | 5.6864 ms |  69.169 ms | 6000.0000 | 3875.0000 | 1500.0000 |   62.3 MB |
| MpackSerialize                 | 100000 |  11.776 ms | 0.2327 ms | 0.3338 ms |  11.808 ms |  484.3750 |  484.3750 |  484.3750 |  15.83 MB |
| JsonSerializeAndDeserialize    | 100000 | 178.753 ms | 3.5128 ms | 6.5979 ms | 177.648 ms | 8000.0000 | 4250.0000 | 1250.0000 |  82.62 MB |
| MpackSerializeAndDeserialize   | 100000 |  26.440 ms | 0.6105 ms | 1.7808 ms |  26.394 ms | 1468.7500 | 1406.2500 |  906.2500 |  21.17 MB |
| MpackSerializeAndDeserializeV2 | 100000 |         NA |        NA |        NA |         NA |        NA |        NA |        NA |        NA |

Benchmarks with issues:
  MessagePackVSJson.MpackSerializeAndDeserializeV2: DefaultJob [Amount=10000]
  MessagePackVSJson.MpackSerializeAndDeserializeV2: DefaultJob [Amount=100000]

// * Warnings *
Environment
  Summary -> Detected error exit code from one of the benchmarks. It might be caused by following antivirus software:
        - Windows Defender (windowsdefender://)
Use InProcessEmitToolchain or InProcessNoEmitToolchain to avoid new process creation.

MultimodalDistribution
  MessagePackVSJson.MpackSerializeAndDeserialize: Default -> It seems that the distribution is bimodal (mValue = 3.52)

// * Hints *
Outliers
  MessagePackVSJson.JsonSerialize: Default                -> 1 outlier  was  removed (5.49 ms)
  MessagePackVSJson.MpackSerialize: Default               -> 4 outliers were removed (1.78 ms..1.88 ms)
  MessagePackVSJson.JsonSerializeAndDeserialize: Default  -> 2 outliers were removed (21.96 ms, 22.25 ms)
  MessagePackVSJson.MpackSerializeAndDeserialize: Default -> 4 outliers were removed (4.42 ms..4.55 ms)
  MessagePackVSJson.JsonSerialize: Default                -> 8 outliers were removed (85.60 ms..96.18 ms)
  MessagePackVSJson.JsonSerializeAndDeserialize: Default  -> 7 outliers were removed (203.29 ms..226.73 ms)
  MessagePackVSJson.MpackSerializeAndDeserialize: Default -> 2 outliers were removed (32.08 ms, 32.92 ms)

// * Legends *
  Amount    : Value of the 'Amount' parameter
  Mean      : Arithmetic mean of all measurements
  Error     : Half of 99.9% confidence interval
  StdDev    : Standard deviation of all measurements
  Median    : Value separating the higher half of all measurements (50th percentile)
  Gen0      : GC Generation 0 collects per 1000 operations
  Gen1      : GC Generation 1 collects per 1000 operations
  Gen2      : GC Generation 2 collects per 1000 operations
  Allocated : Allocated memory per single operation (managed only, inclusive, 1KB = 1024B)
  1 ms      : 1 Millisecond (0.001 sec)

// * Diagnostic Output - MemoryDiagnoser *


// ***** BenchmarkRunner: End *****
Run time: 00:08:18 (498.98 sec), executed benchmarks: 10

Global total time: 00:08:26 (506.73 sec), executed benchmarks: 10
// * Artifacts cleanup *
Artifacts cleanup is finished
