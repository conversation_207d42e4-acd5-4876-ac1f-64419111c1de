// Validating benchmarks:
// ***** BenchmarkRunner: Start   *****
// ***** Found 8 benchmark(s) in total *****
// ***** Building 1 exe(s) in Parallel: Start   *****
// start dotnet  restore /p:UseSharedCompilation=false /p:BuildInParallel=false /m:1 /p:Deterministic=true /p:Optimize=true /p:IntermediateOutputPath="D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\obj\Release\net9.0/" /p:OutDir="D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0/" /p:OutputPath="D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0/" in D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe
// command took 1.52 sec and exited with 0
// start dotnet  build -c Release --no-restore /p:UseSharedCompilation=false /p:BuildInParallel=false /m:1 /p:Deterministic=true /p:Optimize=true /p:IntermediateOutputPath="D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\obj\Release\net9.0/" /p:OutDir="D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0/" /p:OutputPath="D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0/" --output "D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0/" in D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe
// command took 6.19 sec and exited with 0
// ***** Done, took 00:00:07 (7.79 sec)   *****
// Found 8 benchmarks:
//   MessagePackVSJson.JsonSerialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.MpackSerialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=10000]
//   MessagePackVSJson.JsonSerialize: DefaultJob [Amount=100000]
//   MessagePackVSJson.MpackSerialize: DefaultJob [Amount=100000]
//   MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=100000]
//   MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=100000]

Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.JsonSerialize: DefaultJob [Amount=10000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 1fafdae0-bd3e-464d-a091-84dc35d0c0fe.dll --anonymousPipes 1376 1420 --benchmarkName "Sample.Benchmark.MessagePackVSJson.JsonSerialize(Amount: 10000)" --job Default --benchmarkId 0 in D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 188700.00 ns, 188.7000 us/op
WorkloadJitting  1: 1 op, 113042500.00 ns, 113.0425 ms/op

WorkloadPilot    1: 2 op, 57948400.00 ns, 28.9742 ms/op
WorkloadPilot    2: 3 op, 80994400.00 ns, 26.9981 ms/op
WorkloadPilot    3: 4 op, 86001300.00 ns, 21.5003 ms/op
WorkloadPilot    4: 5 op, 27246800.00 ns, 5.4494 ms/op
WorkloadPilot    5: 6 op, 33498800.00 ns, 5.5831 ms/op
WorkloadPilot    6: 7 op, 61656500.00 ns, 8.8081 ms/op
WorkloadPilot    7: 8 op, 60736600.00 ns, 7.5921 ms/op
WorkloadPilot    8: 9 op, 57413200.00 ns, 6.3792 ms/op
WorkloadPilot    9: 10 op, 57773100.00 ns, 5.7773 ms/op
WorkloadPilot   10: 11 op, 65453400.00 ns, 5.9503 ms/op
WorkloadPilot   11: 12 op, 64742600.00 ns, 5.3952 ms/op
WorkloadPilot   12: 13 op, 68476900.00 ns, 5.2675 ms/op
WorkloadPilot   13: 14 op, 81279300.00 ns, 5.8057 ms/op
WorkloadPilot   14: 15 op, 80140300.00 ns, 5.3427 ms/op
WorkloadPilot   15: 16 op, 84635600.00 ns, 5.2897 ms/op
WorkloadPilot   16: 32 op, 164341100.00 ns, 5.1357 ms/op
WorkloadPilot   17: 64 op, 338205000.00 ns, 5.2845 ms/op
WorkloadPilot   18: 128 op, 774553700.00 ns, 6.0512 ms/op

WorkloadWarmup   1: 128 op, 963698500.00 ns, 7.5289 ms/op
WorkloadWarmup   2: 128 op, 821917900.00 ns, 6.4212 ms/op
WorkloadWarmup   3: 128 op, 711361100.00 ns, 5.5575 ms/op
WorkloadWarmup   4: 128 op, 736222500.00 ns, 5.7517 ms/op
WorkloadWarmup   5: 128 op, 918689800.00 ns, 7.1773 ms/op
WorkloadWarmup   6: 128 op, 747232400.00 ns, 5.8378 ms/op
WorkloadWarmup   7: 128 op, 674626000.00 ns, 5.2705 ms/op
WorkloadWarmup   8: 128 op, 682345300.00 ns, 5.3308 ms/op
WorkloadWarmup   9: 128 op, 1077292100.00 ns, 8.4163 ms/op
WorkloadWarmup  10: 128 op, 851130200.00 ns, 6.6495 ms/op

// BeforeActualRun
WorkloadActual   1: 128 op, 823086300.00 ns, 6.4304 ms/op
WorkloadActual   2: 128 op, 774512100.00 ns, 6.0509 ms/op
WorkloadActual   3: 128 op, 897140600.00 ns, 7.0089 ms/op
WorkloadActual   4: 128 op, 878685700.00 ns, 6.8647 ms/op
WorkloadActual   5: 128 op, 816487100.00 ns, 6.3788 ms/op
WorkloadActual   6: 128 op, 860535500.00 ns, 6.7229 ms/op
WorkloadActual   7: 128 op, 747498800.00 ns, 5.8398 ms/op
WorkloadActual   8: 128 op, 748150800.00 ns, 5.8449 ms/op
WorkloadActual   9: 128 op, 771487600.00 ns, 6.0272 ms/op
WorkloadActual  10: 128 op, 750600000.00 ns, 5.8641 ms/op
WorkloadActual  11: 128 op, 759859700.00 ns, 5.9364 ms/op
WorkloadActual  12: 128 op, 727750900.00 ns, 5.6856 ms/op
WorkloadActual  13: 128 op, 928940100.00 ns, 7.2573 ms/op
WorkloadActual  14: 128 op, 838663200.00 ns, 6.5521 ms/op
WorkloadActual  15: 128 op, 860672500.00 ns, 6.7240 ms/op
WorkloadActual  16: 128 op, 839377400.00 ns, 6.5576 ms/op
WorkloadActual  17: 128 op, 812127600.00 ns, 6.3447 ms/op
WorkloadActual  18: 128 op, 769559800.00 ns, 6.0122 ms/op
WorkloadActual  19: 128 op, 782839500.00 ns, 6.1159 ms/op
WorkloadActual  20: 128 op, 729578200.00 ns, 5.6998 ms/op
WorkloadActual  21: 128 op, 738825300.00 ns, 5.7721 ms/op
WorkloadActual  22: 128 op, 690149900.00 ns, 5.3918 ms/op
WorkloadActual  23: 128 op, 677353800.00 ns, 5.2918 ms/op
WorkloadActual  24: 128 op, 661683600.00 ns, 5.1694 ms/op
WorkloadActual  25: 128 op, 674273800.00 ns, 5.2678 ms/op
WorkloadActual  26: 128 op, 664736400.00 ns, 5.1933 ms/op
WorkloadActual  27: 128 op, 683466300.00 ns, 5.3396 ms/op
WorkloadActual  28: 128 op, 686339700.00 ns, 5.3620 ms/op
WorkloadActual  29: 128 op, 649111900.00 ns, 5.0712 ms/op
WorkloadActual  30: 128 op, 636716200.00 ns, 4.9743 ms/op
WorkloadActual  31: 128 op, 634082700.00 ns, 4.9538 ms/op
WorkloadActual  32: 128 op, 634107300.00 ns, 4.9540 ms/op
WorkloadActual  33: 128 op, 633354700.00 ns, 4.9481 ms/op
WorkloadActual  34: 128 op, 636957200.00 ns, 4.9762 ms/op
WorkloadActual  35: 128 op, 643558300.00 ns, 5.0278 ms/op
WorkloadActual  36: 128 op, 643641600.00 ns, 5.0285 ms/op
WorkloadActual  37: 128 op, 646557300.00 ns, 5.0512 ms/op
WorkloadActual  38: 128 op, 640816600.00 ns, 5.0064 ms/op
WorkloadActual  39: 128 op, 637431300.00 ns, 4.9799 ms/op
WorkloadActual  40: 128 op, 639432600.00 ns, 4.9956 ms/op
WorkloadActual  41: 128 op, 675942200.00 ns, 5.2808 ms/op
WorkloadActual  42: 128 op, 676619900.00 ns, 5.2861 ms/op
WorkloadActual  43: 128 op, 718370700.00 ns, 5.6123 ms/op
WorkloadActual  44: 128 op, 674375100.00 ns, 5.2686 ms/op
WorkloadActual  45: 128 op, 662874200.00 ns, 5.1787 ms/op
WorkloadActual  46: 128 op, 670292700.00 ns, 5.2367 ms/op
WorkloadActual  47: 128 op, 665912200.00 ns, 5.2024 ms/op
WorkloadActual  48: 128 op, 665795000.00 ns, 5.2015 ms/op
WorkloadActual  49: 128 op, 654312200.00 ns, 5.1118 ms/op
WorkloadActual  50: 128 op, 650398800.00 ns, 5.0812 ms/op
WorkloadActual  51: 128 op, 668983600.00 ns, 5.2264 ms/op
WorkloadActual  52: 128 op, 681866200.00 ns, 5.3271 ms/op
WorkloadActual  53: 128 op, 700313700.00 ns, 5.4712 ms/op
WorkloadActual  54: 128 op, 676065400.00 ns, 5.2818 ms/op
WorkloadActual  55: 128 op, 853411200.00 ns, 6.6673 ms/op
WorkloadActual  56: 128 op, 677139700.00 ns, 5.2902 ms/op
WorkloadActual  57: 128 op, 753459600.00 ns, 5.8864 ms/op
WorkloadActual  58: 128 op, 921539700.00 ns, 7.1995 ms/op
WorkloadActual  59: 128 op, 854382400.00 ns, 6.6749 ms/op
WorkloadActual  60: 128 op, 698767500.00 ns, 5.4591 ms/op
WorkloadActual  61: 128 op, 679487100.00 ns, 5.3085 ms/op
WorkloadActual  62: 128 op, 695662400.00 ns, 5.4349 ms/op
WorkloadActual  63: 128 op, 690741500.00 ns, 5.3964 ms/op
WorkloadActual  64: 128 op, 879488500.00 ns, 6.8710 ms/op
WorkloadActual  65: 128 op, 778380600.00 ns, 6.0811 ms/op
WorkloadActual  66: 128 op, 675393400.00 ns, 5.2765 ms/op
WorkloadActual  67: 128 op, 664057200.00 ns, 5.1879 ms/op
WorkloadActual  68: 128 op, 673577100.00 ns, 5.2623 ms/op
WorkloadActual  69: 128 op, 722267600.00 ns, 5.6427 ms/op
WorkloadActual  70: 128 op, 681817500.00 ns, 5.3267 ms/op
WorkloadActual  71: 128 op, 664274400.00 ns, 5.1896 ms/op
WorkloadActual  72: 128 op, 687689300.00 ns, 5.3726 ms/op
WorkloadActual  73: 128 op, 685737700.00 ns, 5.3573 ms/op
WorkloadActual  74: 128 op, 836142600.00 ns, 6.5324 ms/op
WorkloadActual  75: 128 op, 889512500.00 ns, 6.9493 ms/op
WorkloadActual  76: 128 op, 686760700.00 ns, 5.3653 ms/op
WorkloadActual  77: 128 op, 690093300.00 ns, 5.3914 ms/op
WorkloadActual  78: 128 op, 688547800.00 ns, 5.3793 ms/op
WorkloadActual  79: 128 op, 701644300.00 ns, 5.4816 ms/op
WorkloadActual  80: 128 op, 774239300.00 ns, 6.0487 ms/op
WorkloadActual  81: 128 op, 867734900.00 ns, 6.7792 ms/op
WorkloadActual  82: 128 op, 680818700.00 ns, 5.3189 ms/op
WorkloadActual  83: 128 op, 699501300.00 ns, 5.4649 ms/op
WorkloadActual  84: 128 op, 696751900.00 ns, 5.4434 ms/op
WorkloadActual  85: 128 op, 674966100.00 ns, 5.2732 ms/op
WorkloadActual  86: 128 op, 748139100.00 ns, 5.8448 ms/op
WorkloadActual  87: 128 op, 878917200.00 ns, 6.8665 ms/op
WorkloadActual  88: 128 op, 720306300.00 ns, 5.6274 ms/op
WorkloadActual  89: 128 op, 671882600.00 ns, 5.2491 ms/op
WorkloadActual  90: 128 op, 666945400.00 ns, 5.2105 ms/op
WorkloadActual  91: 128 op, 674150400.00 ns, 5.2668 ms/op
WorkloadActual  92: 128 op, 721712000.00 ns, 5.6384 ms/op
WorkloadActual  93: 128 op, 710950200.00 ns, 5.5543 ms/op
WorkloadActual  94: 128 op, 686117100.00 ns, 5.3603 ms/op
WorkloadActual  95: 128 op, 836800200.00 ns, 6.5375 ms/op
WorkloadActual  96: 128 op, 930256000.00 ns, 7.2676 ms/op
WorkloadActual  97: 128 op, 881239900.00 ns, 6.8847 ms/op
WorkloadActual  98: 128 op, 844830200.00 ns, 6.6002 ms/op
WorkloadActual  99: 128 op, 906422100.00 ns, 7.0814 ms/op
WorkloadActual  100: 128 op, 810768200.00 ns, 6.3341 ms/op

// AfterActualRun
WorkloadResult   1: 128 op, 823086300.00 ns, 6.4304 ms/op
WorkloadResult   2: 128 op, 774512100.00 ns, 6.0509 ms/op
WorkloadResult   3: 128 op, 897140600.00 ns, 7.0089 ms/op
WorkloadResult   4: 128 op, 878685700.00 ns, 6.8647 ms/op
WorkloadResult   5: 128 op, 816487100.00 ns, 6.3788 ms/op
WorkloadResult   6: 128 op, 860535500.00 ns, 6.7229 ms/op
WorkloadResult   7: 128 op, 747498800.00 ns, 5.8398 ms/op
WorkloadResult   8: 128 op, 748150800.00 ns, 5.8449 ms/op
WorkloadResult   9: 128 op, 771487600.00 ns, 6.0272 ms/op
WorkloadResult  10: 128 op, 750600000.00 ns, 5.8641 ms/op
WorkloadResult  11: 128 op, 759859700.00 ns, 5.9364 ms/op
WorkloadResult  12: 128 op, 727750900.00 ns, 5.6856 ms/op
WorkloadResult  13: 128 op, 928940100.00 ns, 7.2573 ms/op
WorkloadResult  14: 128 op, 838663200.00 ns, 6.5521 ms/op
WorkloadResult  15: 128 op, 860672500.00 ns, 6.7240 ms/op
WorkloadResult  16: 128 op, 839377400.00 ns, 6.5576 ms/op
WorkloadResult  17: 128 op, 812127600.00 ns, 6.3447 ms/op
WorkloadResult  18: 128 op, 769559800.00 ns, 6.0122 ms/op
WorkloadResult  19: 128 op, 782839500.00 ns, 6.1159 ms/op
WorkloadResult  20: 128 op, 729578200.00 ns, 5.6998 ms/op
WorkloadResult  21: 128 op, 738825300.00 ns, 5.7721 ms/op
WorkloadResult  22: 128 op, 690149900.00 ns, 5.3918 ms/op
WorkloadResult  23: 128 op, 677353800.00 ns, 5.2918 ms/op
WorkloadResult  24: 128 op, 661683600.00 ns, 5.1694 ms/op
WorkloadResult  25: 128 op, 674273800.00 ns, 5.2678 ms/op
WorkloadResult  26: 128 op, 664736400.00 ns, 5.1933 ms/op
WorkloadResult  27: 128 op, 683466300.00 ns, 5.3396 ms/op
WorkloadResult  28: 128 op, 686339700.00 ns, 5.3620 ms/op
WorkloadResult  29: 128 op, 649111900.00 ns, 5.0712 ms/op
WorkloadResult  30: 128 op, 636716200.00 ns, 4.9743 ms/op
WorkloadResult  31: 128 op, 634082700.00 ns, 4.9538 ms/op
WorkloadResult  32: 128 op, 634107300.00 ns, 4.9540 ms/op
WorkloadResult  33: 128 op, 633354700.00 ns, 4.9481 ms/op
WorkloadResult  34: 128 op, 636957200.00 ns, 4.9762 ms/op
WorkloadResult  35: 128 op, 643558300.00 ns, 5.0278 ms/op
WorkloadResult  36: 128 op, 643641600.00 ns, 5.0285 ms/op
WorkloadResult  37: 128 op, 646557300.00 ns, 5.0512 ms/op
WorkloadResult  38: 128 op, 640816600.00 ns, 5.0064 ms/op
WorkloadResult  39: 128 op, 637431300.00 ns, 4.9799 ms/op
WorkloadResult  40: 128 op, 639432600.00 ns, 4.9956 ms/op
WorkloadResult  41: 128 op, 675942200.00 ns, 5.2808 ms/op
WorkloadResult  42: 128 op, 676619900.00 ns, 5.2861 ms/op
WorkloadResult  43: 128 op, 718370700.00 ns, 5.6123 ms/op
WorkloadResult  44: 128 op, 674375100.00 ns, 5.2686 ms/op
WorkloadResult  45: 128 op, 662874200.00 ns, 5.1787 ms/op
WorkloadResult  46: 128 op, 670292700.00 ns, 5.2367 ms/op
WorkloadResult  47: 128 op, 665912200.00 ns, 5.2024 ms/op
WorkloadResult  48: 128 op, 665795000.00 ns, 5.2015 ms/op
WorkloadResult  49: 128 op, 654312200.00 ns, 5.1118 ms/op
WorkloadResult  50: 128 op, 650398800.00 ns, 5.0812 ms/op
WorkloadResult  51: 128 op, 668983600.00 ns, 5.2264 ms/op
WorkloadResult  52: 128 op, 681866200.00 ns, 5.3271 ms/op
WorkloadResult  53: 128 op, 700313700.00 ns, 5.4712 ms/op
WorkloadResult  54: 128 op, 676065400.00 ns, 5.2818 ms/op
WorkloadResult  55: 128 op, 853411200.00 ns, 6.6673 ms/op
WorkloadResult  56: 128 op, 677139700.00 ns, 5.2902 ms/op
WorkloadResult  57: 128 op, 753459600.00 ns, 5.8864 ms/op
WorkloadResult  58: 128 op, 921539700.00 ns, 7.1995 ms/op
WorkloadResult  59: 128 op, 854382400.00 ns, 6.6749 ms/op
WorkloadResult  60: 128 op, 698767500.00 ns, 5.4591 ms/op
WorkloadResult  61: 128 op, 679487100.00 ns, 5.3085 ms/op
WorkloadResult  62: 128 op, 695662400.00 ns, 5.4349 ms/op
WorkloadResult  63: 128 op, 690741500.00 ns, 5.3964 ms/op
WorkloadResult  64: 128 op, 879488500.00 ns, 6.8710 ms/op
WorkloadResult  65: 128 op, 778380600.00 ns, 6.0811 ms/op
WorkloadResult  66: 128 op, 675393400.00 ns, 5.2765 ms/op
WorkloadResult  67: 128 op, 664057200.00 ns, 5.1879 ms/op
WorkloadResult  68: 128 op, 673577100.00 ns, 5.2623 ms/op
WorkloadResult  69: 128 op, 722267600.00 ns, 5.6427 ms/op
WorkloadResult  70: 128 op, 681817500.00 ns, 5.3267 ms/op
WorkloadResult  71: 128 op, 664274400.00 ns, 5.1896 ms/op
WorkloadResult  72: 128 op, 687689300.00 ns, 5.3726 ms/op
WorkloadResult  73: 128 op, 685737700.00 ns, 5.3573 ms/op
WorkloadResult  74: 128 op, 836142600.00 ns, 6.5324 ms/op
WorkloadResult  75: 128 op, 889512500.00 ns, 6.9493 ms/op
WorkloadResult  76: 128 op, 686760700.00 ns, 5.3653 ms/op
WorkloadResult  77: 128 op, 690093300.00 ns, 5.3914 ms/op
WorkloadResult  78: 128 op, 688547800.00 ns, 5.3793 ms/op
WorkloadResult  79: 128 op, 701644300.00 ns, 5.4816 ms/op
WorkloadResult  80: 128 op, 774239300.00 ns, 6.0487 ms/op
WorkloadResult  81: 128 op, 867734900.00 ns, 6.7792 ms/op
WorkloadResult  82: 128 op, 680818700.00 ns, 5.3189 ms/op
WorkloadResult  83: 128 op, 699501300.00 ns, 5.4649 ms/op
WorkloadResult  84: 128 op, 696751900.00 ns, 5.4434 ms/op
WorkloadResult  85: 128 op, 674966100.00 ns, 5.2732 ms/op
WorkloadResult  86: 128 op, 748139100.00 ns, 5.8448 ms/op
WorkloadResult  87: 128 op, 878917200.00 ns, 6.8665 ms/op
WorkloadResult  88: 128 op, 720306300.00 ns, 5.6274 ms/op
WorkloadResult  89: 128 op, 671882600.00 ns, 5.2491 ms/op
WorkloadResult  90: 128 op, 666945400.00 ns, 5.2105 ms/op
WorkloadResult  91: 128 op, 674150400.00 ns, 5.2668 ms/op
WorkloadResult  92: 128 op, 721712000.00 ns, 5.6384 ms/op
WorkloadResult  93: 128 op, 710950200.00 ns, 5.5543 ms/op
WorkloadResult  94: 128 op, 686117100.00 ns, 5.3603 ms/op
WorkloadResult  95: 128 op, 836800200.00 ns, 6.5375 ms/op
WorkloadResult  96: 128 op, 930256000.00 ns, 7.2676 ms/op
WorkloadResult  97: 128 op, 881239900.00 ns, 6.8847 ms/op
WorkloadResult  98: 128 op, 844830200.00 ns, 6.6002 ms/op
WorkloadResult  99: 128 op, 906422100.00 ns, 7.0814 ms/op
WorkloadResult  100: 128 op, 810768200.00 ns, 6.3341 ms/op
// GC:  116 106 57 837950288 128
// Threading:  0 0 128

// AfterAll
// Benchmark Process 29260 has exited with code 0.

Mean = 5.721 ms, StdErr = 0.066 ms (1.15%), N = 100, StdDev = 0.657 ms
Min = 4.948 ms, Q1 = 5.259 ms, Median = 5.416 ms, Q3 = 6.090 ms, Max = 7.268 ms
IQR = 0.831 ms, LowerFence = 4.013 ms, UpperFence = 7.336 ms
ConfidenceInterval = [5.498 ms; 5.944 ms] (CI 99.9%), Margin = 0.223 ms (3.90% of Mean)
Skewness = 0.85, Kurtosis = 2.41, MValue = 2.59

// ** Remained 7 (87.5%) benchmark(s) to run. Estimated finish 2024-12-24 14:24 (0h 10m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.MpackSerialize: DefaultJob [Amount=10000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 1fafdae0-bd3e-464d-a091-84dc35d0c0fe.dll --anonymousPipes 1500 1504 --benchmarkName "Sample.Benchmark.MessagePackVSJson.MpackSerialize(Amount: 10000)" --job Default --benchmarkId 1 in D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 180400.00 ns, 180.4000 us/op
WorkloadJitting  1: 1 op, 111049000.00 ns, 111.0490 ms/op

WorkloadPilot    1: 2 op, 7757900.00 ns, 3.8790 ms/op
WorkloadPilot    2: 3 op, 10479900.00 ns, 3.4933 ms/op
WorkloadPilot    3: 4 op, 14707000.00 ns, 3.6768 ms/op
WorkloadPilot    4: 5 op, 17647500.00 ns, 3.5295 ms/op
WorkloadPilot    5: 6 op, 20885900.00 ns, 3.4810 ms/op
WorkloadPilot    6: 7 op, 24119200.00 ns, 3.4456 ms/op
WorkloadPilot    7: 8 op, 29009800.00 ns, 3.6262 ms/op
WorkloadPilot    8: 9 op, 17546400.00 ns, 1.9496 ms/op
WorkloadPilot    9: 10 op, 13494200.00 ns, 1.3494 ms/op
WorkloadPilot   10: 11 op, 14333300.00 ns, 1.3030 ms/op
WorkloadPilot   11: 12 op, 18245200.00 ns, 1.5204 ms/op
WorkloadPilot   12: 13 op, 18876100.00 ns, 1.4520 ms/op
WorkloadPilot   13: 14 op, 18724700.00 ns, 1.3375 ms/op
WorkloadPilot   14: 15 op, 19442400.00 ns, 1.2962 ms/op
WorkloadPilot   15: 16 op, 19767900.00 ns, 1.2355 ms/op
WorkloadPilot   16: 32 op, 43233300.00 ns, 1.3510 ms/op
WorkloadPilot   17: 64 op, 85800000.00 ns, 1.3406 ms/op
WorkloadPilot   18: 128 op, 152872800.00 ns, 1.1943 ms/op
WorkloadPilot   19: 256 op, 301409400.00 ns, 1.1774 ms/op
WorkloadPilot   20: 512 op, 605386900.00 ns, 1.1824 ms/op

WorkloadWarmup   1: 512 op, 641594700.00 ns, 1.2531 ms/op
WorkloadWarmup   2: 512 op, 679027200.00 ns, 1.3262 ms/op
WorkloadWarmup   3: 512 op, 677918400.00 ns, 1.3241 ms/op
WorkloadWarmup   4: 512 op, 683222200.00 ns, 1.3344 ms/op
WorkloadWarmup   5: 512 op, 682209500.00 ns, 1.3324 ms/op
WorkloadWarmup   6: 512 op, 669003700.00 ns, 1.3066 ms/op

// BeforeActualRun
WorkloadActual   1: 512 op, 684773200.00 ns, 1.3374 ms/op
WorkloadActual   2: 512 op, 686305300.00 ns, 1.3404 ms/op
WorkloadActual   3: 512 op, 703360400.00 ns, 1.3738 ms/op
WorkloadActual   4: 512 op, 684194200.00 ns, 1.3363 ms/op
WorkloadActual   5: 512 op, 680771900.00 ns, 1.3296 ms/op
WorkloadActual   6: 512 op, 720158200.00 ns, 1.4066 ms/op
WorkloadActual   7: 512 op, 683546800.00 ns, 1.3351 ms/op
WorkloadActual   8: 512 op, 688611700.00 ns, 1.3449 ms/op
WorkloadActual   9: 512 op, 692971900.00 ns, 1.3535 ms/op
WorkloadActual  10: 512 op, 677298400.00 ns, 1.3228 ms/op
WorkloadActual  11: 512 op, 673724400.00 ns, 1.3159 ms/op
WorkloadActual  12: 512 op, 647349900.00 ns, 1.2644 ms/op
WorkloadActual  13: 512 op, 668374100.00 ns, 1.3054 ms/op
WorkloadActual  14: 512 op, 676270600.00 ns, 1.3208 ms/op
WorkloadActual  15: 512 op, 641620500.00 ns, 1.2532 ms/op
WorkloadActual  16: 512 op, 629385300.00 ns, 1.2293 ms/op
WorkloadActual  17: 512 op, 644541200.00 ns, 1.2589 ms/op
WorkloadActual  18: 512 op, 644821700.00 ns, 1.2594 ms/op
WorkloadActual  19: 512 op, 630159800.00 ns, 1.2308 ms/op
WorkloadActual  20: 512 op, 599853400.00 ns, 1.1716 ms/op
WorkloadActual  21: 512 op, 619430300.00 ns, 1.2098 ms/op
WorkloadActual  22: 512 op, 617275000.00 ns, 1.2056 ms/op
WorkloadActual  23: 512 op, 621185900.00 ns, 1.2133 ms/op
WorkloadActual  24: 512 op, 608736700.00 ns, 1.1889 ms/op
WorkloadActual  25: 512 op, 628862200.00 ns, 1.2282 ms/op
WorkloadActual  26: 512 op, 659074300.00 ns, 1.2873 ms/op
WorkloadActual  27: 512 op, 689774800.00 ns, 1.3472 ms/op
WorkloadActual  28: 512 op, 700199000.00 ns, 1.3676 ms/op
WorkloadActual  29: 512 op, 666804400.00 ns, 1.3024 ms/op
WorkloadActual  30: 512 op, 668117300.00 ns, 1.3049 ms/op
WorkloadActual  31: 512 op, 663091500.00 ns, 1.2951 ms/op
WorkloadActual  32: 512 op, 697726700.00 ns, 1.3627 ms/op
WorkloadActual  33: 512 op, 716204500.00 ns, 1.3988 ms/op
WorkloadActual  34: 512 op, 673438900.00 ns, 1.3153 ms/op
WorkloadActual  35: 512 op, 655864000.00 ns, 1.2810 ms/op
WorkloadActual  36: 512 op, 656450200.00 ns, 1.2821 ms/op
WorkloadActual  37: 512 op, 655082600.00 ns, 1.2795 ms/op
WorkloadActual  38: 512 op, 654768200.00 ns, 1.2788 ms/op
WorkloadActual  39: 512 op, 653067900.00 ns, 1.2755 ms/op
WorkloadActual  40: 512 op, 672366000.00 ns, 1.3132 ms/op
WorkloadActual  41: 512 op, 656728400.00 ns, 1.2827 ms/op
WorkloadActual  42: 512 op, 649703900.00 ns, 1.2690 ms/op
WorkloadActual  43: 512 op, 652038100.00 ns, 1.2735 ms/op
WorkloadActual  44: 512 op, 655679000.00 ns, 1.2806 ms/op
WorkloadActual  45: 512 op, 664542100.00 ns, 1.2979 ms/op
WorkloadActual  46: 512 op, 704654400.00 ns, 1.3763 ms/op
WorkloadActual  47: 512 op, 685415200.00 ns, 1.3387 ms/op
WorkloadActual  48: 512 op, 640911300.00 ns, 1.2518 ms/op
WorkloadActual  49: 512 op, 638716600.00 ns, 1.2475 ms/op
WorkloadActual  50: 512 op, 658145400.00 ns, 1.2854 ms/op
WorkloadActual  51: 512 op, 648967000.00 ns, 1.2675 ms/op
WorkloadActual  52: 512 op, 646179600.00 ns, 1.2621 ms/op

// AfterActualRun
WorkloadResult   1: 512 op, 684773200.00 ns, 1.3374 ms/op
WorkloadResult   2: 512 op, 686305300.00 ns, 1.3404 ms/op
WorkloadResult   3: 512 op, 703360400.00 ns, 1.3738 ms/op
WorkloadResult   4: 512 op, 684194200.00 ns, 1.3363 ms/op
WorkloadResult   5: 512 op, 680771900.00 ns, 1.3296 ms/op
WorkloadResult   6: 512 op, 720158200.00 ns, 1.4066 ms/op
WorkloadResult   7: 512 op, 683546800.00 ns, 1.3351 ms/op
WorkloadResult   8: 512 op, 688611700.00 ns, 1.3449 ms/op
WorkloadResult   9: 512 op, 692971900.00 ns, 1.3535 ms/op
WorkloadResult  10: 512 op, 677298400.00 ns, 1.3228 ms/op
WorkloadResult  11: 512 op, 673724400.00 ns, 1.3159 ms/op
WorkloadResult  12: 512 op, 647349900.00 ns, 1.2644 ms/op
WorkloadResult  13: 512 op, 668374100.00 ns, 1.3054 ms/op
WorkloadResult  14: 512 op, 676270600.00 ns, 1.3208 ms/op
WorkloadResult  15: 512 op, 641620500.00 ns, 1.2532 ms/op
WorkloadResult  16: 512 op, 629385300.00 ns, 1.2293 ms/op
WorkloadResult  17: 512 op, 644541200.00 ns, 1.2589 ms/op
WorkloadResult  18: 512 op, 644821700.00 ns, 1.2594 ms/op
WorkloadResult  19: 512 op, 630159800.00 ns, 1.2308 ms/op
WorkloadResult  20: 512 op, 599853400.00 ns, 1.1716 ms/op
WorkloadResult  21: 512 op, 619430300.00 ns, 1.2098 ms/op
WorkloadResult  22: 512 op, 617275000.00 ns, 1.2056 ms/op
WorkloadResult  23: 512 op, 621185900.00 ns, 1.2133 ms/op
WorkloadResult  24: 512 op, 608736700.00 ns, 1.1889 ms/op
WorkloadResult  25: 512 op, 628862200.00 ns, 1.2282 ms/op
WorkloadResult  26: 512 op, 659074300.00 ns, 1.2873 ms/op
WorkloadResult  27: 512 op, 689774800.00 ns, 1.3472 ms/op
WorkloadResult  28: 512 op, 700199000.00 ns, 1.3676 ms/op
WorkloadResult  29: 512 op, 666804400.00 ns, 1.3024 ms/op
WorkloadResult  30: 512 op, 668117300.00 ns, 1.3049 ms/op
WorkloadResult  31: 512 op, 663091500.00 ns, 1.2951 ms/op
WorkloadResult  32: 512 op, 697726700.00 ns, 1.3627 ms/op
WorkloadResult  33: 512 op, 716204500.00 ns, 1.3988 ms/op
WorkloadResult  34: 512 op, 673438900.00 ns, 1.3153 ms/op
WorkloadResult  35: 512 op, 655864000.00 ns, 1.2810 ms/op
WorkloadResult  36: 512 op, 656450200.00 ns, 1.2821 ms/op
WorkloadResult  37: 512 op, 655082600.00 ns, 1.2795 ms/op
WorkloadResult  38: 512 op, 654768200.00 ns, 1.2788 ms/op
WorkloadResult  39: 512 op, 653067900.00 ns, 1.2755 ms/op
WorkloadResult  40: 512 op, 672366000.00 ns, 1.3132 ms/op
WorkloadResult  41: 512 op, 656728400.00 ns, 1.2827 ms/op
WorkloadResult  42: 512 op, 649703900.00 ns, 1.2690 ms/op
WorkloadResult  43: 512 op, 652038100.00 ns, 1.2735 ms/op
WorkloadResult  44: 512 op, 655679000.00 ns, 1.2806 ms/op
WorkloadResult  45: 512 op, 664542100.00 ns, 1.2979 ms/op
WorkloadResult  46: 512 op, 704654400.00 ns, 1.3763 ms/op
WorkloadResult  47: 512 op, 685415200.00 ns, 1.3387 ms/op
WorkloadResult  48: 512 op, 640911300.00 ns, 1.2518 ms/op
WorkloadResult  49: 512 op, 638716600.00 ns, 1.2475 ms/op
WorkloadResult  50: 512 op, 658145400.00 ns, 1.2854 ms/op
WorkloadResult  51: 512 op, 648967000.00 ns, 1.2675 ms/op
WorkloadResult  52: 512 op, 646179600.00 ns, 1.2621 ms/op
// GC:  116 116 116 850263768 512
// Threading:  0 0 512

// AfterAll
// Benchmark Process 4344 has exited with code 0.

Mean = 1.293 ms, StdErr = 0.007 ms (0.57%), N = 52, StdDev = 0.053 ms
Min = 1.172 ms, Q1 = 1.261 ms, Median = 1.286 ms, Q3 = 1.335 ms, Max = 1.407 ms
IQR = 0.074 ms, LowerFence = 1.150 ms, UpperFence = 1.446 ms
ConfidenceInterval = [1.268 ms; 1.319 ms] (CI 99.9%), Margin = 0.026 ms (1.98% of Mean)
Skewness = -0.06, Kurtosis = 2.53, MValue = 2

// ** Remained 6 (75.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:21 (0h 6m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=10000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 1fafdae0-bd3e-464d-a091-84dc35d0c0fe.dll --anonymousPipes 1500 1412 --benchmarkName "Sample.Benchmark.MessagePackVSJson.JsonSerializeAndDeserialize(Amount: 10000)" --job Default --benchmarkId 2 in D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 182100.00 ns, 182.1000 us/op
WorkloadJitting  1: 1 op, 173541700.00 ns, 173.5417 ms/op

WorkloadPilot    1: 2 op, 185401700.00 ns, 92.7009 ms/op
WorkloadPilot    2: 3 op, 190512400.00 ns, 63.5041 ms/op
WorkloadPilot    3: 4 op, 202188300.00 ns, 50.5471 ms/op
WorkloadPilot    4: 5 op, 79465200.00 ns, 15.8930 ms/op
WorkloadPilot    5: 6 op, 89089100.00 ns, 14.8482 ms/op
WorkloadPilot    6: 7 op, 122814100.00 ns, 17.5449 ms/op
WorkloadPilot    7: 8 op, 133171900.00 ns, 16.6465 ms/op
WorkloadPilot    8: 9 op, 133367100.00 ns, 14.8186 ms/op
WorkloadPilot    9: 10 op, 170142300.00 ns, 17.0142 ms/op
WorkloadPilot   10: 11 op, 178568200.00 ns, 16.2335 ms/op
WorkloadPilot   11: 12 op, 194687700.00 ns, 16.2240 ms/op
WorkloadPilot   12: 13 op, 196785900.00 ns, 15.1374 ms/op
WorkloadPilot   13: 14 op, 210062000.00 ns, 15.0044 ms/op
WorkloadPilot   14: 15 op, 229238100.00 ns, 15.2825 ms/op
WorkloadPilot   15: 16 op, 262627500.00 ns, 16.4142 ms/op
WorkloadPilot   16: 32 op, 523060800.00 ns, 16.3457 ms/op

WorkloadWarmup   1: 32 op, 510715000.00 ns, 15.9598 ms/op
WorkloadWarmup   2: 32 op, 509932000.00 ns, 15.9354 ms/op
WorkloadWarmup   3: 32 op, 507058900.00 ns, 15.8456 ms/op
WorkloadWarmup   4: 32 op, 497818500.00 ns, 15.5568 ms/op
WorkloadWarmup   5: 32 op, 504758300.00 ns, 15.7737 ms/op
WorkloadWarmup   6: 32 op, 539961100.00 ns, 16.8738 ms/op
WorkloadWarmup   7: 32 op, 487657600.00 ns, 15.2393 ms/op
WorkloadWarmup   8: 32 op, 507563400.00 ns, 15.8614 ms/op
WorkloadWarmup   9: 32 op, 512933700.00 ns, 16.0292 ms/op
WorkloadWarmup  10: 32 op, 501188600.00 ns, 15.6621 ms/op

// BeforeActualRun
WorkloadActual   1: 32 op, 497600600.00 ns, 15.5500 ms/op
WorkloadActual   2: 32 op, 504953700.00 ns, 15.7798 ms/op
WorkloadActual   3: 32 op, 506245500.00 ns, 15.8202 ms/op
WorkloadActual   4: 32 op, 493424800.00 ns, 15.4195 ms/op
WorkloadActual   5: 32 op, 502196200.00 ns, 15.6936 ms/op
WorkloadActual   6: 32 op, 509094100.00 ns, 15.9092 ms/op
WorkloadActual   7: 32 op, 515636300.00 ns, 16.1136 ms/op
WorkloadActual   8: 32 op, 492960700.00 ns, 15.4050 ms/op
WorkloadActual   9: 32 op, 480904700.00 ns, 15.0283 ms/op
WorkloadActual  10: 32 op, 491890600.00 ns, 15.3716 ms/op
WorkloadActual  11: 32 op, 514949600.00 ns, 16.0922 ms/op
WorkloadActual  12: 32 op, 502959600.00 ns, 15.7175 ms/op
WorkloadActual  13: 32 op, 510044100.00 ns, 15.9389 ms/op
WorkloadActual  14: 32 op, 519882200.00 ns, 16.2463 ms/op
WorkloadActual  15: 32 op, 504281000.00 ns, 15.7588 ms/op
WorkloadActual  16: 32 op, 495075700.00 ns, 15.4711 ms/op
WorkloadActual  17: 32 op, 499092300.00 ns, 15.5966 ms/op

// AfterActualRun
WorkloadResult   1: 32 op, 497600600.00 ns, 15.5500 ms/op
WorkloadResult   2: 32 op, 504953700.00 ns, 15.7798 ms/op
WorkloadResult   3: 32 op, 506245500.00 ns, 15.8202 ms/op
WorkloadResult   4: 32 op, 493424800.00 ns, 15.4195 ms/op
WorkloadResult   5: 32 op, 502196200.00 ns, 15.6936 ms/op
WorkloadResult   6: 32 op, 509094100.00 ns, 15.9092 ms/op
WorkloadResult   7: 32 op, 515636300.00 ns, 16.1136 ms/op
WorkloadResult   8: 32 op, 492960700.00 ns, 15.4050 ms/op
WorkloadResult   9: 32 op, 480904700.00 ns, 15.0283 ms/op
WorkloadResult  10: 32 op, 491890600.00 ns, 15.3716 ms/op
WorkloadResult  11: 32 op, 514949600.00 ns, 16.0922 ms/op
WorkloadResult  12: 32 op, 502959600.00 ns, 15.7175 ms/op
WorkloadResult  13: 32 op, 510044100.00 ns, 15.9389 ms/op
WorkloadResult  14: 32 op, 519882200.00 ns, 16.2463 ms/op
WorkloadResult  15: 32 op, 504281000.00 ns, 15.7588 ms/op
WorkloadResult  16: 32 op, 495075700.00 ns, 15.4711 ms/op
WorkloadResult  17: 32 op, 499092300.00 ns, 15.5966 ms/op
// GC:  38 32 16 279400056 32
// Threading:  0 0 32

// AfterAll
// Benchmark Process 27828 has exited with code 0.

Mean = 15.701 ms, StdErr = 0.076 ms (0.48%), N = 17, StdDev = 0.312 ms
Min = 15.028 ms, Q1 = 15.471 ms, Median = 15.717 ms, Q3 = 15.909 ms, Max = 16.246 ms
IQR = 0.438 ms, LowerFence = 14.814 ms, UpperFence = 16.566 ms
ConfidenceInterval = [15.397 ms; 16.005 ms] (CI 99.9%), Margin = 0.304 ms (1.94% of Mean)
Skewness = -0.17, Kurtosis = 2.35, MValue = 2

// ** Remained 5 (62.5%) benchmark(s) to run. Estimated finish 2024-12-24 14:19 (0h 4m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=10000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 1fafdae0-bd3e-464d-a091-84dc35d0c0fe.dll --anonymousPipes 1528 1516 --benchmarkName "Sample.Benchmark.MessagePackVSJson.MpackSerializeAndDeserialize(Amount: 10000)" --job Default --benchmarkId 3 in D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 170300.00 ns, 170.3000 us/op
WorkloadJitting  1: 1 op, 123171000.00 ns, 123.1710 ms/op

WorkloadPilot    1: 2 op, 33246000.00 ns, 16.6230 ms/op
WorkloadPilot    2: 3 op, 49377200.00 ns, 16.4591 ms/op
WorkloadPilot    3: 4 op, 65411100.00 ns, 16.3528 ms/op
WorkloadPilot    4: 5 op, 61359200.00 ns, 12.2718 ms/op
WorkloadPilot    5: 6 op, 17532700.00 ns, 2.9221 ms/op
WorkloadPilot    6: 7 op, 18867700.00 ns, 2.6954 ms/op
WorkloadPilot    7: 8 op, 23434600.00 ns, 2.9293 ms/op
WorkloadPilot    8: 9 op, 24550300.00 ns, 2.7278 ms/op
WorkloadPilot    9: 10 op, 28311400.00 ns, 2.8311 ms/op
WorkloadPilot   10: 11 op, 29479100.00 ns, 2.6799 ms/op
WorkloadPilot   11: 12 op, 32847000.00 ns, 2.7373 ms/op
WorkloadPilot   12: 13 op, 34052600.00 ns, 2.6194 ms/op
WorkloadPilot   13: 14 op, 38518600.00 ns, 2.7513 ms/op
WorkloadPilot   14: 15 op, 39030700.00 ns, 2.6020 ms/op
WorkloadPilot   15: 16 op, 41489400.00 ns, 2.5931 ms/op
WorkloadPilot   16: 32 op, 83479100.00 ns, 2.6087 ms/op
WorkloadPilot   17: 64 op, 163937400.00 ns, 2.5615 ms/op
WorkloadPilot   18: 128 op, 322984200.00 ns, 2.5233 ms/op
WorkloadPilot   19: 256 op, 623418700.00 ns, 2.4352 ms/op

WorkloadWarmup   1: 256 op, 619579700.00 ns, 2.4202 ms/op
WorkloadWarmup   2: 256 op, 616505400.00 ns, 2.4082 ms/op
WorkloadWarmup   3: 256 op, 617852300.00 ns, 2.4135 ms/op
WorkloadWarmup   4: 256 op, 618333700.00 ns, 2.4154 ms/op
WorkloadWarmup   5: 256 op, 613346500.00 ns, 2.3959 ms/op
WorkloadWarmup   6: 256 op, 624054000.00 ns, 2.4377 ms/op
WorkloadWarmup   7: 256 op, 614465000.00 ns, 2.4003 ms/op

// BeforeActualRun
WorkloadActual   1: 256 op, 625109200.00 ns, 2.4418 ms/op
WorkloadActual   2: 256 op, 622181700.00 ns, 2.4304 ms/op
WorkloadActual   3: 256 op, 623623800.00 ns, 2.4360 ms/op
WorkloadActual   4: 256 op, 619878100.00 ns, 2.4214 ms/op
WorkloadActual   5: 256 op, 618574100.00 ns, 2.4163 ms/op
WorkloadActual   6: 256 op, 624947800.00 ns, 2.4412 ms/op
WorkloadActual   7: 256 op, 618830100.00 ns, 2.4173 ms/op
WorkloadActual   8: 256 op, 617570300.00 ns, 2.4124 ms/op
WorkloadActual   9: 256 op, 628426900.00 ns, 2.4548 ms/op
WorkloadActual  10: 256 op, 706982100.00 ns, 2.7616 ms/op
WorkloadActual  11: 256 op, 689951400.00 ns, 2.6951 ms/op
WorkloadActual  12: 256 op, 629878700.00 ns, 2.4605 ms/op
WorkloadActual  13: 256 op, 625521200.00 ns, 2.4434 ms/op
WorkloadActual  14: 256 op, 632278100.00 ns, 2.4698 ms/op
WorkloadActual  15: 256 op, 622266600.00 ns, 2.4307 ms/op

// AfterActualRun
WorkloadResult   1: 256 op, 625109200.00 ns, 2.4418 ms/op
WorkloadResult   2: 256 op, 622181700.00 ns, 2.4304 ms/op
WorkloadResult   3: 256 op, 623623800.00 ns, 2.4360 ms/op
WorkloadResult   4: 256 op, 619878100.00 ns, 2.4214 ms/op
WorkloadResult   5: 256 op, 618574100.00 ns, 2.4163 ms/op
WorkloadResult   6: 256 op, 624947800.00 ns, 2.4412 ms/op
WorkloadResult   7: 256 op, 618830100.00 ns, 2.4173 ms/op
WorkloadResult   8: 256 op, 617570300.00 ns, 2.4124 ms/op
WorkloadResult   9: 256 op, 628426900.00 ns, 2.4548 ms/op
WorkloadResult  10: 256 op, 629878700.00 ns, 2.4605 ms/op
WorkloadResult  11: 256 op, 625521200.00 ns, 2.4434 ms/op
WorkloadResult  12: 256 op, 632278100.00 ns, 2.4698 ms/op
WorkloadResult  13: 256 op, 622266600.00 ns, 2.4307 ms/op
// GC:  83 74 66 568548096 256
// Threading:  0 0 256

// AfterAll
// Benchmark Process 29108 has exited with code 0.

Mean = 2.437 ms, StdErr = 0.005 ms (0.20%), N = 13, StdDev = 0.018 ms
Min = 2.412 ms, Q1 = 2.421 ms, Median = 2.436 ms, Q3 = 2.443 ms, Max = 2.470 ms
IQR = 0.022 ms, LowerFence = 2.388 ms, UpperFence = 2.477 ms
ConfidenceInterval = [2.415 ms; 2.458 ms] (CI 99.9%), Margin = 0.021 ms (0.87% of Mean)
Skewness = 0.32, Kurtosis = 1.84, MValue = 2

// ** Remained 4 (50.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:18 (0h 2m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.JsonSerialize: DefaultJob [Amount=100000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 1fafdae0-bd3e-464d-a091-84dc35d0c0fe.dll --anonymousPipes 1572 1524 --benchmarkName "Sample.Benchmark.MessagePackVSJson.JsonSerialize(Amount: 100000)" --job Default --benchmarkId 4 in D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 176900.00 ns, 176.9000 us/op
WorkloadJitting  1: 1 op, 283160500.00 ns, 283.1605 ms/op

WorkloadPilot    1: 2 op, 143266100.00 ns, 71.6331 ms/op
WorkloadPilot    2: 3 op, 214483700.00 ns, 71.4946 ms/op
WorkloadPilot    3: 4 op, 253101400.00 ns, 63.2754 ms/op
WorkloadPilot    4: 5 op, 315346400.00 ns, 63.0693 ms/op
WorkloadPilot    5: 6 op, 368991200.00 ns, 61.4985 ms/op
WorkloadPilot    6: 7 op, 466445200.00 ns, 66.6350 ms/op
WorkloadPilot    7: 8 op, 542480300.00 ns, 67.8100 ms/op

WorkloadWarmup   1: 8 op, 646973100.00 ns, 80.8716 ms/op
WorkloadWarmup   2: 8 op, 559181100.00 ns, 69.8976 ms/op
WorkloadWarmup   3: 8 op, 582609300.00 ns, 72.8262 ms/op
WorkloadWarmup   4: 8 op, 553235000.00 ns, 69.1544 ms/op
WorkloadWarmup   5: 8 op, 556772600.00 ns, 69.5966 ms/op
WorkloadWarmup   6: 8 op, 543708600.00 ns, 67.9636 ms/op

// BeforeActualRun
WorkloadActual   1: 8 op, 603525700.00 ns, 75.4407 ms/op
WorkloadActual   2: 8 op, 639034500.00 ns, 79.8793 ms/op
WorkloadActual   3: 8 op, 560598800.00 ns, 70.0749 ms/op
WorkloadActual   4: 8 op, 636737400.00 ns, 79.5922 ms/op
WorkloadActual   5: 8 op, 663670000.00 ns, 82.9588 ms/op
WorkloadActual   6: 8 op, 595137100.00 ns, 74.3921 ms/op
WorkloadActual   7: 8 op, 561694800.00 ns, 70.2119 ms/op
WorkloadActual   8: 8 op, 616692200.00 ns, 77.0865 ms/op
WorkloadActual   9: 8 op, 563724700.00 ns, 70.4656 ms/op
WorkloadActual  10: 8 op, 557606400.00 ns, 69.7008 ms/op
WorkloadActual  11: 8 op, 586568000.00 ns, 73.3210 ms/op
WorkloadActual  12: 8 op, 592187600.00 ns, 74.0235 ms/op
WorkloadActual  13: 8 op, 571080800.00 ns, 71.3851 ms/op
WorkloadActual  14: 8 op, 586260900.00 ns, 73.2826 ms/op
WorkloadActual  15: 8 op, 749528000.00 ns, 93.6910 ms/op
WorkloadActual  16: 8 op, 651898400.00 ns, 81.4873 ms/op
WorkloadActual  17: 8 op, 705624600.00 ns, 88.2031 ms/op
WorkloadActual  18: 8 op, 701520700.00 ns, 87.6901 ms/op
WorkloadActual  19: 8 op, 608766500.00 ns, 76.0958 ms/op
WorkloadActual  20: 8 op, 564184700.00 ns, 70.5231 ms/op
WorkloadActual  21: 8 op, 589124900.00 ns, 73.6406 ms/op
WorkloadActual  22: 8 op, 584213900.00 ns, 73.0267 ms/op
WorkloadActual  23: 8 op, 640516600.00 ns, 80.0646 ms/op
WorkloadActual  24: 8 op, 586324800.00 ns, 73.2906 ms/op
WorkloadActual  25: 8 op, 631623600.00 ns, 78.9530 ms/op
WorkloadActual  26: 8 op, 611049100.00 ns, 76.3811 ms/op
WorkloadActual  27: 8 op, 594663100.00 ns, 74.3329 ms/op
WorkloadActual  28: 8 op, 593676800.00 ns, 74.2096 ms/op
WorkloadActual  29: 8 op, 658183500.00 ns, 82.2729 ms/op
WorkloadActual  30: 8 op, 612538800.00 ns, 76.5674 ms/op
WorkloadActual  31: 8 op, 591223800.00 ns, 73.9030 ms/op
WorkloadActual  32: 8 op, 557710800.00 ns, 69.7139 ms/op
WorkloadActual  33: 8 op, 612244200.00 ns, 76.5305 ms/op
WorkloadActual  34: 8 op, 669011000.00 ns, 83.6264 ms/op
WorkloadActual  35: 8 op, 642304600.00 ns, 80.2881 ms/op
WorkloadActual  36: 8 op, 542293000.00 ns, 67.7866 ms/op
WorkloadActual  37: 8 op, 537117800.00 ns, 67.1397 ms/op
WorkloadActual  38: 8 op, 580578900.00 ns, 72.5724 ms/op
WorkloadActual  39: 8 op, 554896900.00 ns, 69.3621 ms/op
WorkloadActual  40: 8 op, 530152500.00 ns, 66.2691 ms/op
WorkloadActual  41: 8 op, 531748200.00 ns, 66.4685 ms/op
WorkloadActual  42: 8 op, 535838000.00 ns, 66.9798 ms/op
WorkloadActual  43: 8 op, 528764000.00 ns, 66.0955 ms/op
WorkloadActual  44: 8 op, 523208300.00 ns, 65.4010 ms/op
WorkloadActual  45: 8 op, 543605300.00 ns, 67.9507 ms/op
WorkloadActual  46: 8 op, 606563100.00 ns, 75.8204 ms/op
WorkloadActual  47: 8 op, 667186400.00 ns, 83.3983 ms/op
WorkloadActual  48: 8 op, 626035100.00 ns, 78.2544 ms/op
WorkloadActual  49: 8 op, 539333600.00 ns, 67.4167 ms/op
WorkloadActual  50: 8 op, 526775700.00 ns, 65.8470 ms/op
WorkloadActual  51: 8 op, 524502100.00 ns, 65.5628 ms/op
WorkloadActual  52: 8 op, 602469100.00 ns, 75.3086 ms/op
WorkloadActual  53: 8 op, 587778500.00 ns, 73.4723 ms/op
WorkloadActual  54: 8 op, 538811100.00 ns, 67.3514 ms/op
WorkloadActual  55: 8 op, 540260000.00 ns, 67.5325 ms/op
WorkloadActual  56: 8 op, 509134400.00 ns, 63.6418 ms/op
WorkloadActual  57: 8 op, 526934100.00 ns, 65.8668 ms/op
WorkloadActual  58: 8 op, 533917800.00 ns, 66.7397 ms/op
WorkloadActual  59: 8 op, 506989800.00 ns, 63.3737 ms/op
WorkloadActual  60: 8 op, 590602100.00 ns, 73.8253 ms/op
WorkloadActual  61: 8 op, 491430900.00 ns, 61.4289 ms/op
WorkloadActual  62: 8 op, 536857600.00 ns, 67.1072 ms/op
WorkloadActual  63: 8 op, 539724400.00 ns, 67.4656 ms/op
WorkloadActual  64: 8 op, 514216100.00 ns, 64.2770 ms/op
WorkloadActual  65: 8 op, 511735200.00 ns, 63.9669 ms/op
WorkloadActual  66: 8 op, 509236800.00 ns, 63.6546 ms/op
WorkloadActual  67: 8 op, 491242800.00 ns, 61.4054 ms/op
WorkloadActual  68: 8 op, 498428100.00 ns, 62.3035 ms/op
WorkloadActual  69: 8 op, 495693300.00 ns, 61.9617 ms/op
WorkloadActual  70: 8 op, 509212400.00 ns, 63.6516 ms/op
WorkloadActual  71: 8 op, 498686200.00 ns, 62.3358 ms/op
WorkloadActual  72: 8 op, 506679600.00 ns, 63.3350 ms/op
WorkloadActual  73: 8 op, 499856200.00 ns, 62.4820 ms/op
WorkloadActual  74: 8 op, 493999800.00 ns, 61.7500 ms/op
WorkloadActual  75: 8 op, 525051900.00 ns, 65.6315 ms/op
WorkloadActual  76: 8 op, 497760300.00 ns, 62.2200 ms/op
WorkloadActual  77: 8 op, 491594500.00 ns, 61.4493 ms/op
WorkloadActual  78: 8 op, 490439100.00 ns, 61.3049 ms/op
WorkloadActual  79: 8 op, 508134200.00 ns, 63.5168 ms/op
WorkloadActual  80: 8 op, 500185800.00 ns, 62.5232 ms/op
WorkloadActual  81: 8 op, 537170000.00 ns, 67.1463 ms/op
WorkloadActual  82: 8 op, 503975900.00 ns, 62.9970 ms/op
WorkloadActual  83: 8 op, 493968500.00 ns, 61.7461 ms/op
WorkloadActual  84: 8 op, 509032700.00 ns, 63.6291 ms/op
WorkloadActual  85: 8 op, 498206200.00 ns, 62.2758 ms/op
WorkloadActual  86: 8 op, 484874100.00 ns, 60.6093 ms/op
WorkloadActual  87: 8 op, 523816900.00 ns, 65.4771 ms/op
WorkloadActual  88: 8 op, 511167600.00 ns, 63.8960 ms/op
WorkloadActual  89: 8 op, 488593500.00 ns, 61.0742 ms/op
WorkloadActual  90: 8 op, 497301500.00 ns, 62.1627 ms/op
WorkloadActual  91: 8 op, 522722300.00 ns, 65.3403 ms/op
WorkloadActual  92: 8 op, 490358600.00 ns, 61.2948 ms/op
WorkloadActual  93: 8 op, 539821300.00 ns, 67.4777 ms/op
WorkloadActual  94: 8 op, 534658100.00 ns, 66.8323 ms/op
WorkloadActual  95: 8 op, 523218200.00 ns, 65.4023 ms/op
WorkloadActual  96: 8 op, 519847100.00 ns, 64.9809 ms/op
WorkloadActual  97: 8 op, 510188300.00 ns, 63.7735 ms/op
WorkloadActual  98: 8 op, 527747000.00 ns, 65.9684 ms/op
WorkloadActual  99: 8 op, 491977100.00 ns, 61.4971 ms/op
WorkloadActual  100: 8 op, 524847300.00 ns, 65.6059 ms/op

// AfterActualRun
WorkloadResult   1: 8 op, 603525700.00 ns, 75.4407 ms/op
WorkloadResult   2: 8 op, 639034500.00 ns, 79.8793 ms/op
WorkloadResult   3: 8 op, 560598800.00 ns, 70.0749 ms/op
WorkloadResult   4: 8 op, 636737400.00 ns, 79.5922 ms/op
WorkloadResult   5: 8 op, 663670000.00 ns, 82.9588 ms/op
WorkloadResult   6: 8 op, 595137100.00 ns, 74.3921 ms/op
WorkloadResult   7: 8 op, 561694800.00 ns, 70.2119 ms/op
WorkloadResult   8: 8 op, 616692200.00 ns, 77.0865 ms/op
WorkloadResult   9: 8 op, 563724700.00 ns, 70.4656 ms/op
WorkloadResult  10: 8 op, 557606400.00 ns, 69.7008 ms/op
WorkloadResult  11: 8 op, 586568000.00 ns, 73.3210 ms/op
WorkloadResult  12: 8 op, 592187600.00 ns, 74.0235 ms/op
WorkloadResult  13: 8 op, 571080800.00 ns, 71.3851 ms/op
WorkloadResult  14: 8 op, 586260900.00 ns, 73.2826 ms/op
WorkloadResult  15: 8 op, 651898400.00 ns, 81.4873 ms/op
WorkloadResult  16: 8 op, 705624600.00 ns, 88.2031 ms/op
WorkloadResult  17: 8 op, 701520700.00 ns, 87.6901 ms/op
WorkloadResult  18: 8 op, 608766500.00 ns, 76.0958 ms/op
WorkloadResult  19: 8 op, 564184700.00 ns, 70.5231 ms/op
WorkloadResult  20: 8 op, 589124900.00 ns, 73.6406 ms/op
WorkloadResult  21: 8 op, 584213900.00 ns, 73.0267 ms/op
WorkloadResult  22: 8 op, 640516600.00 ns, 80.0646 ms/op
WorkloadResult  23: 8 op, 586324800.00 ns, 73.2906 ms/op
WorkloadResult  24: 8 op, 631623600.00 ns, 78.9530 ms/op
WorkloadResult  25: 8 op, 611049100.00 ns, 76.3811 ms/op
WorkloadResult  26: 8 op, 594663100.00 ns, 74.3329 ms/op
WorkloadResult  27: 8 op, 593676800.00 ns, 74.2096 ms/op
WorkloadResult  28: 8 op, 658183500.00 ns, 82.2729 ms/op
WorkloadResult  29: 8 op, 612538800.00 ns, 76.5674 ms/op
WorkloadResult  30: 8 op, 591223800.00 ns, 73.9030 ms/op
WorkloadResult  31: 8 op, 557710800.00 ns, 69.7139 ms/op
WorkloadResult  32: 8 op, 612244200.00 ns, 76.5305 ms/op
WorkloadResult  33: 8 op, 669011000.00 ns, 83.6264 ms/op
WorkloadResult  34: 8 op, 642304600.00 ns, 80.2881 ms/op
WorkloadResult  35: 8 op, 542293000.00 ns, 67.7866 ms/op
WorkloadResult  36: 8 op, 537117800.00 ns, 67.1397 ms/op
WorkloadResult  37: 8 op, 580578900.00 ns, 72.5724 ms/op
WorkloadResult  38: 8 op, 554896900.00 ns, 69.3621 ms/op
WorkloadResult  39: 8 op, 530152500.00 ns, 66.2691 ms/op
WorkloadResult  40: 8 op, 531748200.00 ns, 66.4685 ms/op
WorkloadResult  41: 8 op, 535838000.00 ns, 66.9798 ms/op
WorkloadResult  42: 8 op, 528764000.00 ns, 66.0955 ms/op
WorkloadResult  43: 8 op, 523208300.00 ns, 65.4010 ms/op
WorkloadResult  44: 8 op, 543605300.00 ns, 67.9507 ms/op
WorkloadResult  45: 8 op, 606563100.00 ns, 75.8204 ms/op
WorkloadResult  46: 8 op, 667186400.00 ns, 83.3983 ms/op
WorkloadResult  47: 8 op, 626035100.00 ns, 78.2544 ms/op
WorkloadResult  48: 8 op, 539333600.00 ns, 67.4167 ms/op
WorkloadResult  49: 8 op, 526775700.00 ns, 65.8470 ms/op
WorkloadResult  50: 8 op, 524502100.00 ns, 65.5628 ms/op
WorkloadResult  51: 8 op, 602469100.00 ns, 75.3086 ms/op
WorkloadResult  52: 8 op, 587778500.00 ns, 73.4723 ms/op
WorkloadResult  53: 8 op, 538811100.00 ns, 67.3514 ms/op
WorkloadResult  54: 8 op, 540260000.00 ns, 67.5325 ms/op
WorkloadResult  55: 8 op, 509134400.00 ns, 63.6418 ms/op
WorkloadResult  56: 8 op, 526934100.00 ns, 65.8668 ms/op
WorkloadResult  57: 8 op, 533917800.00 ns, 66.7397 ms/op
WorkloadResult  58: 8 op, 506989800.00 ns, 63.3737 ms/op
WorkloadResult  59: 8 op, 590602100.00 ns, 73.8253 ms/op
WorkloadResult  60: 8 op, 491430900.00 ns, 61.4289 ms/op
WorkloadResult  61: 8 op, 536857600.00 ns, 67.1072 ms/op
WorkloadResult  62: 8 op, 539724400.00 ns, 67.4656 ms/op
WorkloadResult  63: 8 op, 514216100.00 ns, 64.2770 ms/op
WorkloadResult  64: 8 op, 511735200.00 ns, 63.9669 ms/op
WorkloadResult  65: 8 op, 509236800.00 ns, 63.6546 ms/op
WorkloadResult  66: 8 op, 491242800.00 ns, 61.4054 ms/op
WorkloadResult  67: 8 op, 498428100.00 ns, 62.3035 ms/op
WorkloadResult  68: 8 op, 495693300.00 ns, 61.9617 ms/op
WorkloadResult  69: 8 op, 509212400.00 ns, 63.6516 ms/op
WorkloadResult  70: 8 op, 498686200.00 ns, 62.3358 ms/op
WorkloadResult  71: 8 op, 506679600.00 ns, 63.3350 ms/op
WorkloadResult  72: 8 op, 499856200.00 ns, 62.4820 ms/op
WorkloadResult  73: 8 op, 493999800.00 ns, 61.7500 ms/op
WorkloadResult  74: 8 op, 525051900.00 ns, 65.6315 ms/op
WorkloadResult  75: 8 op, 497760300.00 ns, 62.2200 ms/op
WorkloadResult  76: 8 op, 491594500.00 ns, 61.4493 ms/op
WorkloadResult  77: 8 op, 490439100.00 ns, 61.3049 ms/op
WorkloadResult  78: 8 op, 508134200.00 ns, 63.5168 ms/op
WorkloadResult  79: 8 op, 500185800.00 ns, 62.5232 ms/op
WorkloadResult  80: 8 op, 537170000.00 ns, 67.1463 ms/op
WorkloadResult  81: 8 op, 503975900.00 ns, 62.9970 ms/op
WorkloadResult  82: 8 op, 493968500.00 ns, 61.7461 ms/op
WorkloadResult  83: 8 op, 509032700.00 ns, 63.6291 ms/op
WorkloadResult  84: 8 op, 498206200.00 ns, 62.2758 ms/op
WorkloadResult  85: 8 op, 484874100.00 ns, 60.6093 ms/op
WorkloadResult  86: 8 op, 523816900.00 ns, 65.4771 ms/op
WorkloadResult  87: 8 op, 511167600.00 ns, 63.8960 ms/op
WorkloadResult  88: 8 op, 488593500.00 ns, 61.0742 ms/op
WorkloadResult  89: 8 op, 497301500.00 ns, 62.1627 ms/op
WorkloadResult  90: 8 op, 522722300.00 ns, 65.3403 ms/op
WorkloadResult  91: 8 op, 490358600.00 ns, 61.2948 ms/op
WorkloadResult  92: 8 op, 539821300.00 ns, 67.4777 ms/op
WorkloadResult  93: 8 op, 534658100.00 ns, 66.8323 ms/op
WorkloadResult  94: 8 op, 523218200.00 ns, 65.4023 ms/op
WorkloadResult  95: 8 op, 519847100.00 ns, 64.9809 ms/op
WorkloadResult  96: 8 op, 510188300.00 ns, 63.7735 ms/op
WorkloadResult  97: 8 op, 527747000.00 ns, 65.9684 ms/op
WorkloadResult  98: 8 op, 491977100.00 ns, 61.4971 ms/op
WorkloadResult  99: 8 op, 524847300.00 ns, 65.6059 ms/op
// GC:  48 31 12 522650816 8
// Threading:  0 0 8

// AfterAll
// Benchmark Process 2836 has exited with code 0.

Mean = 69.344 ms, StdErr = 0.682 ms (0.98%), N = 99, StdDev = 6.787 ms
Min = 60.609 ms, Q1 = 63.653 ms, Median = 67.146 ms, Q3 = 73.963 ms, Max = 88.203 ms
IQR = 10.310 ms, LowerFence = 48.188 ms, UpperFence = 89.428 ms
ConfidenceInterval = [67.030 ms; 71.658 ms] (CI 99.9%), Margin = 2.314 ms (3.34% of Mean)
Skewness = 0.78, Kurtosis = 2.71, MValue = 3.44

// ** Remained 3 (37.5%) benchmark(s) to run. Estimated finish 2024-12-24 14:18 (0h 2m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.MpackSerialize: DefaultJob [Amount=100000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 1fafdae0-bd3e-464d-a091-84dc35d0c0fe.dll --anonymousPipes 1372 1428 --benchmarkName "Sample.Benchmark.MessagePackVSJson.MpackSerialize(Amount: 100000)" --job Default --benchmarkId 5 in D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 225900.00 ns, 225.9000 us/op
WorkloadJitting  1: 1 op, 129664500.00 ns, 129.6645 ms/op

WorkloadPilot    1: 2 op, 62275100.00 ns, 31.1376 ms/op
WorkloadPilot    2: 3 op, 95598300.00 ns, 31.8661 ms/op
WorkloadPilot    3: 4 op, 78241300.00 ns, 19.5603 ms/op
WorkloadPilot    4: 5 op, 58740900.00 ns, 11.7482 ms/op
WorkloadPilot    5: 6 op, 69534300.00 ns, 11.5891 ms/op
WorkloadPilot    6: 7 op, 84816000.00 ns, 12.1166 ms/op
WorkloadPilot    7: 8 op, 92321500.00 ns, 11.5402 ms/op
WorkloadPilot    8: 9 op, 101728000.00 ns, 11.3031 ms/op
WorkloadPilot    9: 10 op, 116235600.00 ns, 11.6236 ms/op
WorkloadPilot   10: 11 op, 122566700.00 ns, 11.1424 ms/op
WorkloadPilot   11: 12 op, 131527800.00 ns, 10.9607 ms/op
WorkloadPilot   12: 13 op, 142004600.00 ns, 10.9234 ms/op
WorkloadPilot   13: 14 op, 151303000.00 ns, 10.8074 ms/op
WorkloadPilot   14: 15 op, 169704700.00 ns, 11.3136 ms/op
WorkloadPilot   15: 16 op, 179510900.00 ns, 11.2194 ms/op
WorkloadPilot   16: 32 op, 360956300.00 ns, 11.2799 ms/op
WorkloadPilot   17: 64 op, 721605500.00 ns, 11.2751 ms/op

WorkloadWarmup   1: 64 op, 709456500.00 ns, 11.0853 ms/op
WorkloadWarmup   2: 64 op, 786382700.00 ns, 12.2872 ms/op
WorkloadWarmup   3: 64 op, 794216900.00 ns, 12.4096 ms/op
WorkloadWarmup   4: 64 op, 717104600.00 ns, 11.2048 ms/op
WorkloadWarmup   5: 64 op, 719397700.00 ns, 11.2406 ms/op
WorkloadWarmup   6: 64 op, 704914600.00 ns, 11.0143 ms/op

// BeforeActualRun
WorkloadActual   1: 64 op, 711267500.00 ns, 11.1136 ms/op
WorkloadActual   2: 64 op, 701241000.00 ns, 10.9569 ms/op
WorkloadActual   3: 64 op, 722941900.00 ns, 11.2960 ms/op
WorkloadActual   4: 64 op, 712514600.00 ns, 11.1330 ms/op
WorkloadActual   5: 64 op, 710472200.00 ns, 11.1011 ms/op
WorkloadActual   6: 64 op, 701978000.00 ns, 10.9684 ms/op
WorkloadActual   7: 64 op, 719745300.00 ns, 11.2460 ms/op
WorkloadActual   8: 64 op, 701505900.00 ns, 10.9610 ms/op
WorkloadActual   9: 64 op, 721808100.00 ns, 11.2783 ms/op
WorkloadActual  10: 64 op, 722872700.00 ns, 11.2949 ms/op
WorkloadActual  11: 64 op, 733993600.00 ns, 11.4687 ms/op
WorkloadActual  12: 64 op, 709349600.00 ns, 11.0836 ms/op
WorkloadActual  13: 64 op, 705583000.00 ns, 11.0247 ms/op
WorkloadActual  14: 64 op, 691634400.00 ns, 10.8068 ms/op
WorkloadActual  15: 64 op, 691555800.00 ns, 10.8056 ms/op

// AfterActualRun
WorkloadResult   1: 64 op, 711267500.00 ns, 11.1136 ms/op
WorkloadResult   2: 64 op, 701241000.00 ns, 10.9569 ms/op
WorkloadResult   3: 64 op, 722941900.00 ns, 11.2960 ms/op
WorkloadResult   4: 64 op, 712514600.00 ns, 11.1330 ms/op
WorkloadResult   5: 64 op, 710472200.00 ns, 11.1011 ms/op
WorkloadResult   6: 64 op, 701978000.00 ns, 10.9684 ms/op
WorkloadResult   7: 64 op, 719745300.00 ns, 11.2460 ms/op
WorkloadResult   8: 64 op, 701505900.00 ns, 10.9610 ms/op
WorkloadResult   9: 64 op, 721808100.00 ns, 11.2783 ms/op
WorkloadResult  10: 64 op, 722872700.00 ns, 11.2949 ms/op
WorkloadResult  11: 64 op, 733993600.00 ns, 11.4687 ms/op
WorkloadResult  12: 64 op, 709349600.00 ns, 11.0836 ms/op
WorkloadResult  13: 64 op, 705583000.00 ns, 11.0247 ms/op
WorkloadResult  14: 64 op, 691634400.00 ns, 10.8068 ms/op
WorkloadResult  15: 64 op, 691555800.00 ns, 10.8056 ms/op
// GC:  31 31 31 1062455352 64
// Threading:  0 0 64

// AfterAll
// Benchmark Process 27172 has exited with code 0.

Mean = 11.103 ms, StdErr = 0.049 ms (0.44%), N = 15, StdDev = 0.189 ms
Min = 10.806 ms, Q1 = 10.965 ms, Median = 11.101 ms, Q3 = 11.262 ms, Max = 11.469 ms
IQR = 0.297 ms, LowerFence = 10.519 ms, UpperFence = 11.708 ms
ConfidenceInterval = [10.900 ms; 11.305 ms] (CI 99.9%), Margin = 0.202 ms (1.82% of Mean)
Skewness = 0.11, Kurtosis = 2.01, MValue = 2

// ** Remained 2 (25.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:18 (0h 1m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=100000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 1fafdae0-bd3e-464d-a091-84dc35d0c0fe.dll --anonymousPipes 1540 1568 --benchmarkName "Sample.Benchmark.MessagePackVSJson.JsonSerializeAndDeserialize(Amount: 100000)" --job Default --benchmarkId 6 in D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 169500.00 ns, 169.5000 us/op
WorkloadJitting  1: 1 op, 615322100.00 ns, 615.3221 ms/op

OverheadJitting  2: 1 op, 700.00 ns, 700.0000 ns/op
WorkloadJitting  2: 1 op, 157259500.00 ns, 157.2595 ms/op

WorkloadPilot    1: 2 op, 320748100.00 ns, 160.3741 ms/op
WorkloadPilot    2: 3 op, 494902300.00 ns, 164.9674 ms/op
WorkloadPilot    3: 4 op, 650727500.00 ns, 162.6819 ms/op

WorkloadWarmup   1: 4 op, 671726000.00 ns, 167.9315 ms/op
WorkloadWarmup   2: 4 op, 688814800.00 ns, 172.2037 ms/op
WorkloadWarmup   3: 4 op, 699654000.00 ns, 174.9135 ms/op
WorkloadWarmup   4: 4 op, 670540400.00 ns, 167.6351 ms/op
WorkloadWarmup   5: 4 op, 679120500.00 ns, 169.7801 ms/op
WorkloadWarmup   6: 4 op, 681499800.00 ns, 170.3750 ms/op
WorkloadWarmup   7: 4 op, 673226500.00 ns, 168.3066 ms/op

// BeforeActualRun
WorkloadActual   1: 4 op, 706953100.00 ns, 176.7383 ms/op
WorkloadActual   2: 4 op, 708171000.00 ns, 177.0428 ms/op
WorkloadActual   3: 4 op, 753925600.00 ns, 188.4814 ms/op
WorkloadActual   4: 4 op, 670183800.00 ns, 167.5460 ms/op
WorkloadActual   5: 4 op, 688100000.00 ns, 172.0250 ms/op
WorkloadActual   6: 4 op, 671711800.00 ns, 167.9280 ms/op
WorkloadActual   7: 4 op, 674595200.00 ns, 168.6488 ms/op
WorkloadActual   8: 4 op, 688287100.00 ns, 172.0718 ms/op
WorkloadActual   9: 4 op, 784586100.00 ns, 196.1465 ms/op
WorkloadActual  10: 4 op, 758504600.00 ns, 189.6262 ms/op
WorkloadActual  11: 4 op, 734963100.00 ns, 183.7408 ms/op
WorkloadActual  12: 4 op, 679454400.00 ns, 169.8636 ms/op
WorkloadActual  13: 4 op, 708793900.00 ns, 177.1985 ms/op
WorkloadActual  14: 4 op, 680340800.00 ns, 170.0852 ms/op
WorkloadActual  15: 4 op, 663552600.00 ns, 165.8882 ms/op
WorkloadActual  16: 4 op, 652342700.00 ns, 163.0857 ms/op
WorkloadActual  17: 4 op, 664999400.00 ns, 166.2499 ms/op
WorkloadActual  18: 4 op, 657457800.00 ns, 164.3645 ms/op
WorkloadActual  19: 4 op, 735780300.00 ns, 183.9451 ms/op
WorkloadActual  20: 4 op, 665469100.00 ns, 166.3673 ms/op
WorkloadActual  21: 4 op, 655783300.00 ns, 163.9458 ms/op
WorkloadActual  22: 4 op, 658463100.00 ns, 164.6158 ms/op
WorkloadActual  23: 4 op, 665023900.00 ns, 166.2560 ms/op
WorkloadActual  24: 4 op, 674025800.00 ns, 168.5065 ms/op
WorkloadActual  25: 4 op, 685974800.00 ns, 171.4937 ms/op
WorkloadActual  26: 4 op, 663492500.00 ns, 165.8731 ms/op
WorkloadActual  27: 4 op, 662324500.00 ns, 165.5811 ms/op
WorkloadActual  28: 4 op, 658478400.00 ns, 164.6196 ms/op
WorkloadActual  29: 4 op, 694555100.00 ns, 173.6388 ms/op
WorkloadActual  30: 4 op, 734607500.00 ns, 183.6519 ms/op
WorkloadActual  31: 4 op, 698179600.00 ns, 174.5449 ms/op
WorkloadActual  32: 4 op, 703470100.00 ns, 175.8675 ms/op
WorkloadActual  33: 4 op, 658615000.00 ns, 164.6538 ms/op
WorkloadActual  34: 4 op, 661166100.00 ns, 165.2915 ms/op
WorkloadActual  35: 4 op, 692229200.00 ns, 173.0573 ms/op
WorkloadActual  36: 4 op, 692040500.00 ns, 173.0101 ms/op
WorkloadActual  37: 4 op, 813408500.00 ns, 203.3521 ms/op
WorkloadActual  38: 4 op, 731902100.00 ns, 182.9755 ms/op
WorkloadActual  39: 4 op, 728192400.00 ns, 182.0481 ms/op
WorkloadActual  40: 4 op, 1089054100.00 ns, 272.2635 ms/op
WorkloadActual  41: 4 op, 740714900.00 ns, 185.1787 ms/op
WorkloadActual  42: 4 op, 752821700.00 ns, 188.2054 ms/op
WorkloadActual  43: 4 op, 733841200.00 ns, 183.4603 ms/op
WorkloadActual  44: 4 op, 684283000.00 ns, 171.0708 ms/op
WorkloadActual  45: 4 op, 693087000.00 ns, 173.2718 ms/op
WorkloadActual  46: 4 op, 693489900.00 ns, 173.3725 ms/op
WorkloadActual  47: 4 op, 730306300.00 ns, 182.5766 ms/op
WorkloadActual  48: 4 op, 755756800.00 ns, 188.9392 ms/op
WorkloadActual  49: 4 op, 763640200.00 ns, 190.9101 ms/op
WorkloadActual  50: 4 op, 862078200.00 ns, 215.5196 ms/op
WorkloadActual  51: 4 op, 720153400.00 ns, 180.0384 ms/op
WorkloadActual  52: 4 op, 966917600.00 ns, 241.7294 ms/op
WorkloadActual  53: 4 op, 970789600.00 ns, 242.6974 ms/op
WorkloadActual  54: 4 op, 756442400.00 ns, 189.1106 ms/op
WorkloadActual  55: 4 op, 692067000.00 ns, 173.0168 ms/op
WorkloadActual  56: 4 op, 753533200.00 ns, 188.3833 ms/op
WorkloadActual  57: 4 op, 692145500.00 ns, 173.0364 ms/op
WorkloadActual  58: 4 op, 729211400.00 ns, 182.3029 ms/op
WorkloadActual  59: 4 op, 694865000.00 ns, 173.7163 ms/op
WorkloadActual  60: 4 op, 705469900.00 ns, 176.3675 ms/op
WorkloadActual  61: 4 op, 698784700.00 ns, 174.6962 ms/op
WorkloadActual  62: 4 op, 702236000.00 ns, 175.5590 ms/op
WorkloadActual  63: 4 op, 859777600.00 ns, 214.9444 ms/op
WorkloadActual  64: 4 op, 825496400.00 ns, 206.3741 ms/op
WorkloadActual  65: 4 op, 760955400.00 ns, 190.2389 ms/op
WorkloadActual  66: 4 op, 740548000.00 ns, 185.1370 ms/op
WorkloadActual  67: 4 op, 677493800.00 ns, 169.3735 ms/op
WorkloadActual  68: 4 op, 697811300.00 ns, 174.4528 ms/op
WorkloadActual  69: 4 op, 786664200.00 ns, 196.6661 ms/op
WorkloadActual  70: 4 op, 773926100.00 ns, 193.4815 ms/op
WorkloadActual  71: 4 op, 874373500.00 ns, 218.5934 ms/op
WorkloadActual  72: 4 op, 783689700.00 ns, 195.9224 ms/op
WorkloadActual  73: 4 op, 808048600.00 ns, 202.0122 ms/op
WorkloadActual  74: 4 op, 810505200.00 ns, 202.6263 ms/op
WorkloadActual  75: 4 op, 714701300.00 ns, 178.6753 ms/op
WorkloadActual  76: 4 op, 919314200.00 ns, 229.8286 ms/op
WorkloadActual  77: 4 op, 687415100.00 ns, 171.8538 ms/op
WorkloadActual  78: 4 op, 738473500.00 ns, 184.6184 ms/op
WorkloadActual  79: 4 op, 807170200.00 ns, 201.7926 ms/op
WorkloadActual  80: 4 op, 764863100.00 ns, 191.2158 ms/op
WorkloadActual  81: 4 op, 728001900.00 ns, 182.0005 ms/op
WorkloadActual  82: 4 op, 738719100.00 ns, 184.6798 ms/op
WorkloadActual  83: 4 op, 689751500.00 ns, 172.4379 ms/op
WorkloadActual  84: 4 op, 729376700.00 ns, 182.3442 ms/op
WorkloadActual  85: 4 op, 765165000.00 ns, 191.2913 ms/op
WorkloadActual  86: 4 op, 708176200.00 ns, 177.0441 ms/op
WorkloadActual  87: 4 op, 714917800.00 ns, 178.7295 ms/op
WorkloadActual  88: 4 op, 710525100.00 ns, 177.6313 ms/op
WorkloadActual  89: 4 op, 696091800.00 ns, 174.0230 ms/op
WorkloadActual  90: 4 op, 715562000.00 ns, 178.8905 ms/op
WorkloadActual  91: 4 op, 754486600.00 ns, 188.6217 ms/op
WorkloadActual  92: 4 op, 724450800.00 ns, 181.1127 ms/op
WorkloadActual  93: 4 op, 731556300.00 ns, 182.8891 ms/op
WorkloadActual  94: 4 op, 688712900.00 ns, 172.1782 ms/op
WorkloadActual  95: 4 op, 725413200.00 ns, 181.3533 ms/op
WorkloadActual  96: 4 op, 748871100.00 ns, 187.2178 ms/op
WorkloadActual  97: 4 op, 701302500.00 ns, 175.3256 ms/op
WorkloadActual  98: 4 op, 685862800.00 ns, 171.4657 ms/op
WorkloadActual  99: 4 op, 694520300.00 ns, 173.6301 ms/op
WorkloadActual  100: 4 op, 695503000.00 ns, 173.8758 ms/op

// AfterActualRun
WorkloadResult   1: 4 op, 706953100.00 ns, 176.7383 ms/op
WorkloadResult   2: 4 op, 708171000.00 ns, 177.0428 ms/op
WorkloadResult   3: 4 op, 753925600.00 ns, 188.4814 ms/op
WorkloadResult   4: 4 op, 670183800.00 ns, 167.5460 ms/op
WorkloadResult   5: 4 op, 688100000.00 ns, 172.0250 ms/op
WorkloadResult   6: 4 op, 671711800.00 ns, 167.9280 ms/op
WorkloadResult   7: 4 op, 674595200.00 ns, 168.6488 ms/op
WorkloadResult   8: 4 op, 688287100.00 ns, 172.0718 ms/op
WorkloadResult   9: 4 op, 784586100.00 ns, 196.1465 ms/op
WorkloadResult  10: 4 op, 758504600.00 ns, 189.6262 ms/op
WorkloadResult  11: 4 op, 734963100.00 ns, 183.7408 ms/op
WorkloadResult  12: 4 op, 679454400.00 ns, 169.8636 ms/op
WorkloadResult  13: 4 op, 708793900.00 ns, 177.1985 ms/op
WorkloadResult  14: 4 op, 680340800.00 ns, 170.0852 ms/op
WorkloadResult  15: 4 op, 663552600.00 ns, 165.8882 ms/op
WorkloadResult  16: 4 op, 652342700.00 ns, 163.0857 ms/op
WorkloadResult  17: 4 op, 664999400.00 ns, 166.2499 ms/op
WorkloadResult  18: 4 op, 657457800.00 ns, 164.3645 ms/op
WorkloadResult  19: 4 op, 735780300.00 ns, 183.9451 ms/op
WorkloadResult  20: 4 op, 665469100.00 ns, 166.3673 ms/op
WorkloadResult  21: 4 op, 655783300.00 ns, 163.9458 ms/op
WorkloadResult  22: 4 op, 658463100.00 ns, 164.6158 ms/op
WorkloadResult  23: 4 op, 665023900.00 ns, 166.2560 ms/op
WorkloadResult  24: 4 op, 674025800.00 ns, 168.5065 ms/op
WorkloadResult  25: 4 op, 685974800.00 ns, 171.4937 ms/op
WorkloadResult  26: 4 op, 663492500.00 ns, 165.8731 ms/op
WorkloadResult  27: 4 op, 662324500.00 ns, 165.5811 ms/op
WorkloadResult  28: 4 op, 658478400.00 ns, 164.6196 ms/op
WorkloadResult  29: 4 op, 694555100.00 ns, 173.6388 ms/op
WorkloadResult  30: 4 op, 734607500.00 ns, 183.6519 ms/op
WorkloadResult  31: 4 op, 698179600.00 ns, 174.5449 ms/op
WorkloadResult  32: 4 op, 703470100.00 ns, 175.8675 ms/op
WorkloadResult  33: 4 op, 658615000.00 ns, 164.6538 ms/op
WorkloadResult  34: 4 op, 661166100.00 ns, 165.2915 ms/op
WorkloadResult  35: 4 op, 692229200.00 ns, 173.0573 ms/op
WorkloadResult  36: 4 op, 692040500.00 ns, 173.0101 ms/op
WorkloadResult  37: 4 op, 813408500.00 ns, 203.3521 ms/op
WorkloadResult  38: 4 op, 731902100.00 ns, 182.9755 ms/op
WorkloadResult  39: 4 op, 728192400.00 ns, 182.0481 ms/op
WorkloadResult  40: 4 op, 740714900.00 ns, 185.1787 ms/op
WorkloadResult  41: 4 op, 752821700.00 ns, 188.2054 ms/op
WorkloadResult  42: 4 op, 733841200.00 ns, 183.4603 ms/op
WorkloadResult  43: 4 op, 684283000.00 ns, 171.0708 ms/op
WorkloadResult  44: 4 op, 693087000.00 ns, 173.2718 ms/op
WorkloadResult  45: 4 op, 693489900.00 ns, 173.3725 ms/op
WorkloadResult  46: 4 op, 730306300.00 ns, 182.5766 ms/op
WorkloadResult  47: 4 op, 755756800.00 ns, 188.9392 ms/op
WorkloadResult  48: 4 op, 763640200.00 ns, 190.9101 ms/op
WorkloadResult  49: 4 op, 720153400.00 ns, 180.0384 ms/op
WorkloadResult  50: 4 op, 756442400.00 ns, 189.1106 ms/op
WorkloadResult  51: 4 op, 692067000.00 ns, 173.0168 ms/op
WorkloadResult  52: 4 op, 753533200.00 ns, 188.3833 ms/op
WorkloadResult  53: 4 op, 692145500.00 ns, 173.0364 ms/op
WorkloadResult  54: 4 op, 729211400.00 ns, 182.3029 ms/op
WorkloadResult  55: 4 op, 694865000.00 ns, 173.7163 ms/op
WorkloadResult  56: 4 op, 705469900.00 ns, 176.3675 ms/op
WorkloadResult  57: 4 op, 698784700.00 ns, 174.6962 ms/op
WorkloadResult  58: 4 op, 702236000.00 ns, 175.5590 ms/op
WorkloadResult  59: 4 op, 825496400.00 ns, 206.3741 ms/op
WorkloadResult  60: 4 op, 760955400.00 ns, 190.2389 ms/op
WorkloadResult  61: 4 op, 740548000.00 ns, 185.1370 ms/op
WorkloadResult  62: 4 op, 677493800.00 ns, 169.3735 ms/op
WorkloadResult  63: 4 op, 697811300.00 ns, 174.4528 ms/op
WorkloadResult  64: 4 op, 786664200.00 ns, 196.6661 ms/op
WorkloadResult  65: 4 op, 773926100.00 ns, 193.4815 ms/op
WorkloadResult  66: 4 op, 783689700.00 ns, 195.9224 ms/op
WorkloadResult  67: 4 op, 808048600.00 ns, 202.0122 ms/op
WorkloadResult  68: 4 op, 810505200.00 ns, 202.6263 ms/op
WorkloadResult  69: 4 op, 714701300.00 ns, 178.6753 ms/op
WorkloadResult  70: 4 op, 687415100.00 ns, 171.8538 ms/op
WorkloadResult  71: 4 op, 738473500.00 ns, 184.6184 ms/op
WorkloadResult  72: 4 op, 807170200.00 ns, 201.7926 ms/op
WorkloadResult  73: 4 op, 764863100.00 ns, 191.2158 ms/op
WorkloadResult  74: 4 op, 728001900.00 ns, 182.0005 ms/op
WorkloadResult  75: 4 op, 738719100.00 ns, 184.6798 ms/op
WorkloadResult  76: 4 op, 689751500.00 ns, 172.4379 ms/op
WorkloadResult  77: 4 op, 729376700.00 ns, 182.3442 ms/op
WorkloadResult  78: 4 op, 765165000.00 ns, 191.2913 ms/op
WorkloadResult  79: 4 op, 708176200.00 ns, 177.0441 ms/op
WorkloadResult  80: 4 op, 714917800.00 ns, 178.7295 ms/op
WorkloadResult  81: 4 op, 710525100.00 ns, 177.6313 ms/op
WorkloadResult  82: 4 op, 696091800.00 ns, 174.0230 ms/op
WorkloadResult  83: 4 op, 715562000.00 ns, 178.8905 ms/op
WorkloadResult  84: 4 op, 754486600.00 ns, 188.6217 ms/op
WorkloadResult  85: 4 op, 724450800.00 ns, 181.1127 ms/op
WorkloadResult  86: 4 op, 731556300.00 ns, 182.8891 ms/op
WorkloadResult  87: 4 op, 688712900.00 ns, 172.1782 ms/op
WorkloadResult  88: 4 op, 725413200.00 ns, 181.3533 ms/op
WorkloadResult  89: 4 op, 748871100.00 ns, 187.2178 ms/op
WorkloadResult  90: 4 op, 701302500.00 ns, 175.3256 ms/op
WorkloadResult  91: 4 op, 685862800.00 ns, 171.4657 ms/op
WorkloadResult  92: 4 op, 694520300.00 ns, 173.6301 ms/op
WorkloadResult  93: 4 op, 695503000.00 ns, 173.8758 ms/op
// GC:  32 17 5 346525840 4
// Threading:  0 0 4

// AfterAll
// Benchmark Process 14628 has exited with code 0.

Mean = 178.757 ms, StdErr = 1.070 ms (0.60%), N = 93, StdDev = 10.315 ms
Min = 163.086 ms, Q1 = 171.854 ms, Median = 176.738 ms, Q3 = 184.680 ms, Max = 206.374 ms
IQR = 12.826 ms, LowerFence = 152.615 ms, UpperFence = 203.919 ms
ConfidenceInterval = [175.121 ms; 182.393 ms] (CI 99.9%), Margin = 3.636 ms (2.03% of Mean)
Skewness = 0.64, Kurtosis = 2.76, MValue = 2.98

// ** Remained 1 (12.5%) benchmark(s) to run. Estimated finish 2024-12-24 14:18 (0h 0m from now) **
Setup power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// **************************
// Benchmark: MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=100000]
// *** Execute ***
// Launch: 1 / 1
// Execute: dotnet 1fafdae0-bd3e-464d-a091-84dc35d0c0fe.dll --anonymousPipes 1540 1568 --benchmarkName "Sample.Benchmark.MessagePackVSJson.MpackSerializeAndDeserialize(Amount: 100000)" --job Default --benchmarkId 7 in D:\代码\Sample.Benchmark\bin\Release\net9.0\1fafdae0-bd3e-464d-a091-84dc35d0c0fe\bin\Release\net9.0
// BeforeAnythingElse

// Benchmark Process Environment Information:
// BenchmarkDotNet v0.14.0
// Runtime=.NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
// GC=Concurrent Workstation
// HardwareIntrinsics=AVX2,AES,BMI1,BMI2,FMA,LZCNT,PCLMUL,POPCNT VectorSize=256
// Job: DefaultJob

OverheadJitting  1: 1 op, 175900.00 ns, 175.9000 us/op
WorkloadJitting  1: 1 op, 282438100.00 ns, 282.4381 ms/op

WorkloadPilot    1: 2 op, 193414300.00 ns, 96.7072 ms/op
WorkloadPilot    2: 3 op, 75322800.00 ns, 25.1076 ms/op
WorkloadPilot    3: 4 op, 96116600.00 ns, 24.0292 ms/op
WorkloadPilot    4: 5 op, 120696300.00 ns, 24.1393 ms/op
WorkloadPilot    5: 6 op, 145047100.00 ns, 24.1745 ms/op
WorkloadPilot    6: 7 op, 173004500.00 ns, 24.7149 ms/op
WorkloadPilot    7: 8 op, 195331200.00 ns, 24.4164 ms/op
WorkloadPilot    8: 9 op, 220679500.00 ns, 24.5199 ms/op
WorkloadPilot    9: 10 op, 253249600.00 ns, 25.3250 ms/op
WorkloadPilot   10: 11 op, 270409800.00 ns, 24.5827 ms/op
WorkloadPilot   11: 12 op, 308376600.00 ns, 25.6981 ms/op
WorkloadPilot   12: 13 op, 328188100.00 ns, 25.2452 ms/op
WorkloadPilot   13: 14 op, 363745200.00 ns, 25.9818 ms/op
WorkloadPilot   14: 15 op, 377652300.00 ns, 25.1768 ms/op
WorkloadPilot   15: 16 op, 465420300.00 ns, 29.0888 ms/op
WorkloadPilot   16: 32 op, 913356700.00 ns, 28.5424 ms/op

WorkloadWarmup   1: 32 op, 787906600.00 ns, 24.6221 ms/op
WorkloadWarmup   2: 32 op, 805223700.00 ns, 25.1632 ms/op
WorkloadWarmup   3: 32 op, 823301600.00 ns, 25.7282 ms/op
WorkloadWarmup   4: 32 op, 810768500.00 ns, 25.3365 ms/op
WorkloadWarmup   5: 32 op, 782444200.00 ns, 24.4514 ms/op
WorkloadWarmup   6: 32 op, 781641200.00 ns, 24.4263 ms/op
WorkloadWarmup   7: 32 op, 819561100.00 ns, 25.6113 ms/op
WorkloadWarmup   8: 32 op, 788069800.00 ns, 24.6272 ms/op

// BeforeActualRun
WorkloadActual   1: 32 op, 848008400.00 ns, 26.5003 ms/op
WorkloadActual   2: 32 op, 836646200.00 ns, 26.1452 ms/op
WorkloadActual   3: 32 op, 799341800.00 ns, 24.9794 ms/op
WorkloadActual   4: 32 op, 805737900.00 ns, 25.1793 ms/op
WorkloadActual   5: 32 op, 781848100.00 ns, 24.4328 ms/op
WorkloadActual   6: 32 op, 793242000.00 ns, 24.7888 ms/op
WorkloadActual   7: 32 op, 771350300.00 ns, 24.1047 ms/op
WorkloadActual   8: 32 op, 790686600.00 ns, 24.7090 ms/op
WorkloadActual   9: 32 op, 794249000.00 ns, 24.8203 ms/op
WorkloadActual  10: 32 op, 783855800.00 ns, 24.4955 ms/op
WorkloadActual  11: 32 op, 795326700.00 ns, 24.8540 ms/op
WorkloadActual  12: 32 op, 785676100.00 ns, 24.5524 ms/op
WorkloadActual  13: 32 op, 787204500.00 ns, 24.6001 ms/op
WorkloadActual  14: 32 op, 879428700.00 ns, 27.4821 ms/op
WorkloadActual  15: 32 op, 849797400.00 ns, 26.5562 ms/op
WorkloadActual  16: 32 op, 888668700.00 ns, 27.7709 ms/op
WorkloadActual  17: 32 op, 993031300.00 ns, 31.0322 ms/op
WorkloadActual  18: 32 op, 837496700.00 ns, 26.1718 ms/op
WorkloadActual  19: 32 op, 826122800.00 ns, 25.8163 ms/op
WorkloadActual  20: 32 op, 845119700.00 ns, 26.4100 ms/op
WorkloadActual  21: 32 op, 939550100.00 ns, 29.3609 ms/op
WorkloadActual  22: 32 op, 870520600.00 ns, 27.2038 ms/op
WorkloadActual  23: 32 op, 863178000.00 ns, 26.9743 ms/op
WorkloadActual  24: 32 op, 844837600.00 ns, 26.4012 ms/op
WorkloadActual  25: 32 op, 845031800.00 ns, 26.4072 ms/op
WorkloadActual  26: 32 op, 910729000.00 ns, 28.4603 ms/op
WorkloadActual  27: 32 op, 825619000.00 ns, 25.8006 ms/op
WorkloadActual  28: 32 op, 828388700.00 ns, 25.8871 ms/op
WorkloadActual  29: 32 op, 850001800.00 ns, 26.5626 ms/op
WorkloadActual  30: 32 op, 848476200.00 ns, 26.5149 ms/op
WorkloadActual  31: 32 op, 920238500.00 ns, 28.7575 ms/op
WorkloadActual  32: 32 op, 826875700.00 ns, 25.8399 ms/op
WorkloadActual  33: 32 op, 831378500.00 ns, 25.9806 ms/op
WorkloadActual  34: 32 op, 823192700.00 ns, 25.7248 ms/op
WorkloadActual  35: 32 op, 831575500.00 ns, 25.9867 ms/op
WorkloadActual  36: 32 op, 857945700.00 ns, 26.8108 ms/op
WorkloadActual  37: 32 op, 993167800.00 ns, 31.0365 ms/op
WorkloadActual  38: 32 op, 1088189600.00 ns, 34.0059 ms/op
WorkloadActual  39: 32 op, 1091292600.00 ns, 34.1029 ms/op
WorkloadActual  40: 32 op, 932736600.00 ns, 29.1480 ms/op
WorkloadActual  41: 32 op, 830401900.00 ns, 25.9501 ms/op
WorkloadActual  42: 32 op, 838848600.00 ns, 26.2140 ms/op
WorkloadActual  43: 32 op, 842785700.00 ns, 26.3371 ms/op
WorkloadActual  44: 32 op, 817063200.00 ns, 25.5332 ms/op
WorkloadActual  45: 32 op, 795701400.00 ns, 24.8657 ms/op
WorkloadActual  46: 32 op, 803193300.00 ns, 25.0998 ms/op
WorkloadActual  47: 32 op, 809734700.00 ns, 25.3042 ms/op
WorkloadActual  48: 32 op, 808699400.00 ns, 25.2719 ms/op
WorkloadActual  49: 32 op, 826949100.00 ns, 25.8422 ms/op
WorkloadActual  50: 32 op, 809893200.00 ns, 25.3092 ms/op
WorkloadActual  51: 32 op, 795068300.00 ns, 24.8459 ms/op
WorkloadActual  52: 32 op, 782813000.00 ns, 24.4629 ms/op
WorkloadActual  53: 32 op, 783015300.00 ns, 24.4692 ms/op
WorkloadActual  54: 32 op, 800629300.00 ns, 25.0197 ms/op
WorkloadActual  55: 32 op, 855654400.00 ns, 26.7392 ms/op
WorkloadActual  56: 32 op, 788573200.00 ns, 24.6429 ms/op
WorkloadActual  57: 32 op, 785826600.00 ns, 24.5571 ms/op
WorkloadActual  58: 32 op, 789441000.00 ns, 24.6700 ms/op
WorkloadActual  59: 32 op, 788102100.00 ns, 24.6282 ms/op

// AfterActualRun
WorkloadResult   1: 32 op, 848008400.00 ns, 26.5003 ms/op
WorkloadResult   2: 32 op, 836646200.00 ns, 26.1452 ms/op
WorkloadResult   3: 32 op, 799341800.00 ns, 24.9794 ms/op
WorkloadResult   4: 32 op, 805737900.00 ns, 25.1793 ms/op
WorkloadResult   5: 32 op, 781848100.00 ns, 24.4328 ms/op
WorkloadResult   6: 32 op, 793242000.00 ns, 24.7888 ms/op
WorkloadResult   7: 32 op, 771350300.00 ns, 24.1047 ms/op
WorkloadResult   8: 32 op, 790686600.00 ns, 24.7090 ms/op
WorkloadResult   9: 32 op, 794249000.00 ns, 24.8203 ms/op
WorkloadResult  10: 32 op, 783855800.00 ns, 24.4955 ms/op
WorkloadResult  11: 32 op, 795326700.00 ns, 24.8540 ms/op
WorkloadResult  12: 32 op, 785676100.00 ns, 24.5524 ms/op
WorkloadResult  13: 32 op, 787204500.00 ns, 24.6001 ms/op
WorkloadResult  14: 32 op, 879428700.00 ns, 27.4821 ms/op
WorkloadResult  15: 32 op, 849797400.00 ns, 26.5562 ms/op
WorkloadResult  16: 32 op, 888668700.00 ns, 27.7709 ms/op
WorkloadResult  17: 32 op, 837496700.00 ns, 26.1718 ms/op
WorkloadResult  18: 32 op, 826122800.00 ns, 25.8163 ms/op
WorkloadResult  19: 32 op, 845119700.00 ns, 26.4100 ms/op
WorkloadResult  20: 32 op, 870520600.00 ns, 27.2038 ms/op
WorkloadResult  21: 32 op, 863178000.00 ns, 26.9743 ms/op
WorkloadResult  22: 32 op, 844837600.00 ns, 26.4012 ms/op
WorkloadResult  23: 32 op, 845031800.00 ns, 26.4072 ms/op
WorkloadResult  24: 32 op, 910729000.00 ns, 28.4603 ms/op
WorkloadResult  25: 32 op, 825619000.00 ns, 25.8006 ms/op
WorkloadResult  26: 32 op, 828388700.00 ns, 25.8871 ms/op
WorkloadResult  27: 32 op, 850001800.00 ns, 26.5626 ms/op
WorkloadResult  28: 32 op, 848476200.00 ns, 26.5149 ms/op
WorkloadResult  29: 32 op, 920238500.00 ns, 28.7575 ms/op
WorkloadResult  30: 32 op, 826875700.00 ns, 25.8399 ms/op
WorkloadResult  31: 32 op, 831378500.00 ns, 25.9806 ms/op
WorkloadResult  32: 32 op, 823192700.00 ns, 25.7248 ms/op
WorkloadResult  33: 32 op, 831575500.00 ns, 25.9867 ms/op
WorkloadResult  34: 32 op, 857945700.00 ns, 26.8108 ms/op
WorkloadResult  35: 32 op, 830401900.00 ns, 25.9501 ms/op
WorkloadResult  36: 32 op, 838848600.00 ns, 26.2140 ms/op
WorkloadResult  37: 32 op, 842785700.00 ns, 26.3371 ms/op
WorkloadResult  38: 32 op, 817063200.00 ns, 25.5332 ms/op
WorkloadResult  39: 32 op, 795701400.00 ns, 24.8657 ms/op
WorkloadResult  40: 32 op, 803193300.00 ns, 25.0998 ms/op
WorkloadResult  41: 32 op, 809734700.00 ns, 25.3042 ms/op
WorkloadResult  42: 32 op, 808699400.00 ns, 25.2719 ms/op
WorkloadResult  43: 32 op, 826949100.00 ns, 25.8422 ms/op
WorkloadResult  44: 32 op, 809893200.00 ns, 25.3092 ms/op
WorkloadResult  45: 32 op, 795068300.00 ns, 24.8459 ms/op
WorkloadResult  46: 32 op, 782813000.00 ns, 24.4629 ms/op
WorkloadResult  47: 32 op, 783015300.00 ns, 24.4692 ms/op
WorkloadResult  48: 32 op, 800629300.00 ns, 25.0197 ms/op
WorkloadResult  49: 32 op, 855654400.00 ns, 26.7392 ms/op
WorkloadResult  50: 32 op, 788573200.00 ns, 24.6429 ms/op
WorkloadResult  51: 32 op, 785826600.00 ns, 24.5571 ms/op
WorkloadResult  52: 32 op, 789441000.00 ns, 24.6700 ms/op
WorkloadResult  53: 32 op, 788102100.00 ns, 24.6282 ms/op
// GC:  47 45 29 710453728 32
// Threading:  0 0 32

// AfterAll
// Benchmark Process 22940 has exited with code 0.

Mean = 25.725 ms, StdErr = 0.146 ms (0.57%), N = 53, StdDev = 1.065 ms
Min = 24.105 ms, Q1 = 24.820 ms, Median = 25.801 ms, Q3 = 26.407 ms, Max = 28.757 ms
IQR = 1.587 ms, LowerFence = 22.440 ms, UpperFence = 28.788 ms
ConfidenceInterval = [25.215 ms; 26.235 ms] (CI 99.9%), Margin = 0.510 ms (1.98% of Mean)
Skewness = 0.73, Kurtosis = 3.1, MValue = 3.16

// ** Remained 0 (0.0%) benchmark(s) to run. Estimated finish 2024-12-24 14:19 (0h 0m from now) **
Successfully reverted power plan (GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c FriendlyName: 高性能)
// ***** BenchmarkRunner: Finish  *****

// * Export *
  BenchmarkDotNet.Artifacts\results\Sample.Benchmark.MessagePackVSJson-report.csv
  BenchmarkDotNet.Artifacts\results\Sample.Benchmark.MessagePackVSJson-report-github.md
  BenchmarkDotNet.Artifacts\results\Sample.Benchmark.MessagePackVSJson-report.html
  BenchmarkDotNet.Artifacts\results\Sample.Benchmark.MessagePackVSJson-report-default.md

// * Detailed results *
MessagePackVSJson.JsonSerialize: DefaultJob [Amount=10000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 5.721 ms, StdErr = 0.066 ms (1.15%), N = 100, StdDev = 0.657 ms
Min = 4.948 ms, Q1 = 5.259 ms, Median = 5.416 ms, Q3 = 6.090 ms, Max = 7.268 ms
IQR = 0.831 ms, LowerFence = 4.013 ms, UpperFence = 7.336 ms
ConfidenceInterval = [5.498 ms; 5.944 ms] (CI 99.9%), Margin = 0.223 ms (3.90% of Mean)
Skewness = 0.85, Kurtosis = 2.41, MValue = 2.59
-------------------- Histogram --------------------
[4.762 ms ; 5.111 ms) | @@@@@@@@@@@@@
[5.111 ms ; 5.589 ms) | @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
[5.589 ms ; 5.960 ms) | @@@@@@@@@@@@@
[5.960 ms ; 6.381 ms) | @@@@@@@@@
[6.381 ms ; 6.523 ms) | @
[6.523 ms ; 6.894 ms) | @@@@@@@@@@@@@@
[6.894 ms ; 7.294 ms) | @@@@@@
---------------------------------------------------

MessagePackVSJson.MpackSerialize: DefaultJob [Amount=10000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 1.293 ms, StdErr = 0.007 ms (0.57%), N = 52, StdDev = 0.053 ms
Min = 1.172 ms, Q1 = 1.261 ms, Median = 1.286 ms, Q3 = 1.335 ms, Max = 1.407 ms
IQR = 0.074 ms, LowerFence = 1.150 ms, UpperFence = 1.446 ms
ConfidenceInterval = [1.268 ms; 1.319 ms] (CI 99.9%), Margin = 0.026 ms (1.98% of Mean)
Skewness = -0.06, Kurtosis = 2.53, MValue = 2
-------------------- Histogram --------------------
[1.162 ms ; 1.200 ms) | @@
[1.200 ms ; 1.251 ms) | @@@@@@@
[1.251 ms ; 1.288 ms) | @@@@@@@@@@@@@@@@@@
[1.288 ms ; 1.349 ms) | @@@@@@@@@@@@@@@@@@
[1.349 ms ; 1.383 ms) | @@@@@
[1.383 ms ; 1.425 ms) | @@
---------------------------------------------------

MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=10000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 15.701 ms, StdErr = 0.076 ms (0.48%), N = 17, StdDev = 0.312 ms
Min = 15.028 ms, Q1 = 15.471 ms, Median = 15.717 ms, Q3 = 15.909 ms, Max = 16.246 ms
IQR = 0.438 ms, LowerFence = 14.814 ms, UpperFence = 16.566 ms
ConfidenceInterval = [15.397 ms; 16.005 ms] (CI 99.9%), Margin = 0.304 ms (1.94% of Mean)
Skewness = -0.17, Kurtosis = 2.35, MValue = 2
-------------------- Histogram --------------------
[14.869 ms ; 15.325 ms) | @
[15.325 ms ; 15.657 ms) | @@@@@@
[15.657 ms ; 16.331 ms) | @@@@@@@@@@
---------------------------------------------------

MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=10000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 2.437 ms, StdErr = 0.005 ms (0.20%), N = 13, StdDev = 0.018 ms
Min = 2.412 ms, Q1 = 2.421 ms, Median = 2.436 ms, Q3 = 2.443 ms, Max = 2.470 ms
IQR = 0.022 ms, LowerFence = 2.388 ms, UpperFence = 2.477 ms
ConfidenceInterval = [2.415 ms; 2.458 ms] (CI 99.9%), Margin = 0.021 ms (0.87% of Mean)
Skewness = 0.32, Kurtosis = 1.84, MValue = 2
-------------------- Histogram --------------------
[2.402 ms ; 2.480 ms) | @@@@@@@@@@@@@
---------------------------------------------------

MessagePackVSJson.JsonSerialize: DefaultJob [Amount=100000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 69.344 ms, StdErr = 0.682 ms (0.98%), N = 99, StdDev = 6.787 ms
Min = 60.609 ms, Q1 = 63.653 ms, Median = 67.146 ms, Q3 = 73.963 ms, Max = 88.203 ms
IQR = 10.310 ms, LowerFence = 48.188 ms, UpperFence = 89.428 ms
ConfidenceInterval = [67.030 ms; 71.658 ms] (CI 99.9%), Margin = 2.314 ms (3.34% of Mean)
Skewness = 0.78, Kurtosis = 2.71, MValue = 3.44
-------------------- Histogram --------------------
[60.138 ms ; 63.628 ms) | @@@@@@@@@@@@@@@@@@@@@@
[63.628 ms ; 67.479 ms) | @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
[67.479 ms ; 70.953 ms) | @@@@@@@@@@
[70.953 ms ; 72.872 ms) | @@
[72.872 ms ; 76.723 ms) | @@@@@@@@@@@@@@@@@@@
[76.723 ms ; 79.827 ms) | @@@@
[79.827 ms ; 83.678 ms) | @@@@@@@@
[83.678 ms ; 86.021 ms) | 
[86.021 ms ; 90.129 ms) | @@
---------------------------------------------------

MessagePackVSJson.MpackSerialize: DefaultJob [Amount=100000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 11.103 ms, StdErr = 0.049 ms (0.44%), N = 15, StdDev = 0.189 ms
Min = 10.806 ms, Q1 = 10.965 ms, Median = 11.101 ms, Q3 = 11.262 ms, Max = 11.469 ms
IQR = 0.297 ms, LowerFence = 10.519 ms, UpperFence = 11.708 ms
ConfidenceInterval = [10.900 ms; 11.305 ms] (CI 99.9%), Margin = 0.202 ms (1.82% of Mean)
Skewness = 0.11, Kurtosis = 2.01, MValue = 2
-------------------- Histogram --------------------
[10.705 ms ; 10.935 ms) | @@
[10.935 ms ; 11.569 ms) | @@@@@@@@@@@@@
---------------------------------------------------

MessagePackVSJson.JsonSerializeAndDeserialize: DefaultJob [Amount=100000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 178.757 ms, StdErr = 1.070 ms (0.60%), N = 93, StdDev = 10.315 ms
Min = 163.086 ms, Q1 = 171.854 ms, Median = 176.738 ms, Q3 = 184.680 ms, Max = 206.374 ms
IQR = 12.826 ms, LowerFence = 152.615 ms, UpperFence = 203.919 ms
ConfidenceInterval = [175.121 ms; 182.393 ms] (CI 99.9%), Margin = 3.636 ms (2.03% of Mean)
Skewness = 0.64, Kurtosis = 2.76, MValue = 2.98
-------------------- Histogram --------------------
[160.098 ms ; 164.237 ms) | @@
[164.237 ms ; 171.344 ms) | @@@@@@@@@@@@@@@@@@@
[171.344 ms ; 177.320 ms) | @@@@@@@@@@@@@@@@@@@@@@@@@@@@@
[177.320 ms ; 185.597 ms) | @@@@@@@@@@@@@@@@@@@@@@
[185.597 ms ; 192.243 ms) | @@@@@@@@@@@@
[192.243 ms ; 201.095 ms) | @@@@
[201.095 ms ; 207.071 ms) | @@@@@
---------------------------------------------------

MessagePackVSJson.MpackSerializeAndDeserialize: DefaultJob [Amount=100000]
Runtime = .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2; GC = Concurrent Workstation
Mean = 25.725 ms, StdErr = 0.146 ms (0.57%), N = 53, StdDev = 1.065 ms
Min = 24.105 ms, Q1 = 24.820 ms, Median = 25.801 ms, Q3 = 26.407 ms, Max = 28.757 ms
IQR = 1.587 ms, LowerFence = 22.440 ms, UpperFence = 28.788 ms
ConfidenceInterval = [25.215 ms; 26.235 ms] (CI 99.9%), Margin = 0.510 ms (1.98% of Mean)
Skewness = 0.73, Kurtosis = 3.1, MValue = 3.16
-------------------- Histogram --------------------
[23.733 ms ; 24.394 ms) | @
[24.394 ms ; 25.138 ms) | @@@@@@@@@@@@@@@@@@@
[25.138 ms ; 25.786 ms) | @@@@@@
[25.786 ms ; 26.530 ms) | @@@@@@@@@@@@@@@@@
[26.530 ms ; 27.252 ms) | @@@@@@
[27.252 ms ; 28.237 ms) | @@
[28.237 ms ; 29.130 ms) | @@
---------------------------------------------------

// * Summary *

BenchmarkDotNet v0.14.0, Windows 11 (10.0.27764.1000)
AMD Ryzen 7 5800H with Radeon Graphics, 1 CPU, 16 logical and 8 physical cores
.NET SDK 9.0.100
  [Host]     : .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2
  DefaultJob : .NET 9.0.0 (9.0.24.52809), X64 RyuJIT AVX2


| Method                       | Amount | Mean       | Error     | StdDev     | Median     | Gen0      | Gen1      | Gen2      | Allocated |
|----------------------------- |------- |-----------:|----------:|-----------:|-----------:|----------:|----------:|----------:|----------:|
| JsonSerialize                | 10000  |   5.721 ms | 0.2228 ms |  0.6571 ms |   5.416 ms |  906.2500 |  828.1250 |  445.3125 |   6.24 MB |
| MpackSerialize               | 10000  |   1.293 ms | 0.0256 ms |  0.0530 ms |   1.286 ms |  226.5625 |  226.5625 |  226.5625 |   1.58 MB |
| JsonSerializeAndDeserialize  | 10000  |  15.701 ms | 0.3040 ms |  0.3122 ms |  15.717 ms | 1187.5000 | 1000.0000 |  500.0000 |   8.33 MB |
| MpackSerializeAndDeserialize | 10000  |   2.437 ms | 0.0212 ms |  0.0177 ms |   2.436 ms |  324.2188 |  289.0625 |  257.8125 |   2.12 MB |
| JsonSerialize                | 100000 |  69.344 ms | 2.3140 ms |  6.7865 ms |  67.146 ms | 6000.0000 | 3875.0000 | 1500.0000 |   62.3 MB |
| MpackSerialize               | 100000 |  11.103 ms | 0.2025 ms |  0.1894 ms |  11.101 ms |  484.3750 |  484.3750 |  484.3750 |  15.83 MB |
| JsonSerializeAndDeserialize  | 100000 | 178.757 ms | 3.6360 ms | 10.3146 ms | 176.738 ms | 8000.0000 | 4250.0000 | 1250.0000 |  82.62 MB |
| MpackSerializeAndDeserialize | 100000 |  25.725 ms | 0.5101 ms |  1.0648 ms |  25.801 ms | 1468.7500 | 1406.2500 |  906.2500 |  21.17 MB |

// * Warnings *
MultimodalDistribution
  MessagePackVSJson.JsonSerialize: Default                -> It seems that the distribution is bimodal (mValue = 3.44)
  MessagePackVSJson.JsonSerializeAndDeserialize: Default  -> It seems that the distribution can have several modes (mValue = 2.98)
  MessagePackVSJson.MpackSerializeAndDeserialize: Default -> It seems that the distribution can have several modes (mValue = 3.16)

// * Hints *
Outliers
  MessagePackVSJson.MpackSerializeAndDeserialize: Default -> 2 outliers were removed (2.70 ms, 2.76 ms)
  MessagePackVSJson.JsonSerialize: Default                -> 1 outlier  was  removed (93.69 ms)
  MessagePackVSJson.JsonSerializeAndDeserialize: Default  -> 7 outliers were removed (214.94 ms..272.26 ms)
  MessagePackVSJson.MpackSerializeAndDeserialize: Default -> 6 outliers were removed (29.15 ms..34.10 ms)

// * Legends *
  Amount    : Value of the 'Amount' parameter
  Mean      : Arithmetic mean of all measurements
  Error     : Half of 99.9% confidence interval
  StdDev    : Standard deviation of all measurements
  Median    : Value separating the higher half of all measurements (50th percentile)
  Gen0      : GC Generation 0 collects per 1000 operations
  Gen1      : GC Generation 1 collects per 1000 operations
  Gen2      : GC Generation 2 collects per 1000 operations
  Allocated : Allocated memory per single operation (managed only, inclusive, 1KB = 1024B)
  1 ms      : 1 Millisecond (0.001 sec)

// * Diagnostic Output - MemoryDiagnoser *


// ***** BenchmarkRunner: End *****
Run time: 00:06:34 (394.89 sec), executed benchmarks: 8

Global total time: 00:06:43 (403.07 sec), executed benchmarks: 8
// * Artifacts cleanup *
Artifacts cleanup is finished
