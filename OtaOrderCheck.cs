using OfficeOpenXml;

namespace Sample.Benchmark;

public class OtaOrderCheck
{
    public void Check()
    { 
        var meiTuanOrders = MeiTuanOrderExcel();
    }
    
    
    // 美团订单excel数据读取(18个excel.excel名称 : 1.xlsx~18.xlsx)
    private List<OtaOrderData> MeiTuanOrderExcel()
    {
        var folderPath = @"C:\Users\<USER>\Desktop\美团";
        var orders = new List<OtaOrderData>();
        for (int i = 1; i <= 18; i++)
        {
            var filePath = Path.Combine(folderPath, $"{i}.xlsx");
            orders.AddRange(ReadMeiTuanOrderExcel(filePath));
        }
        return orders;
    }

    private List<OtaOrderData> ReadMeiTuanOrderExcel(string filePath)
    {
        var orders = new List<OtaOrderData>();
        using (var package = new ExcelPackage(new FileInfo(filePath)))
        {
            var worksheet = package.Workbook.Worksheets[0];
            var rowCount = worksheet.Dimension.Rows;
            for (int row = 2; row <= rowCount; row++)
            {
                // A1 : 渠道单号 '751297953134594' 需要去掉引号
                var channelOrderNo = worksheet.Cells[row, 1].Text;
                if (channelOrderNo.StartsWith("'"))
                {
                    channelOrderNo = channelOrderNo.Substring(1);
                }
                // L1 : 实际支付金额
                var amount = decimal.Parse(worksheet.Cells[row, 12].Text);
                // S1 : 支付状态  `已支付`=> 正常  `未支付`=> 取消  `已退款`=> 退款
                var payStatus = worksheet.Cells[row, 19].Text;
                var status = payStatus switch
                {
                    "已支付" => OtaOrderStatus.Normal,
                    "未支付" => OtaOrderStatus.Cancel,
                    "已退款" => OtaOrderStatus.Refund,
                    _ => OtaOrderStatus.Normal,
                };
                var refundAmount = status == OtaOrderStatus.Refund ? amount : 0m;
                
                var order = new OtaOrderData
                {
                    ChannelOrderNo = channelOrderNo,
                    Status = status,
                    OtaChannelType = OtaChannelType.MeiTuan,
                    Amount = amount,
                    RefundAmount = refundAmount,
                };
                orders.Add(order);
            }
        }
        return orders;
    }
}

/// <summary>
/// 渠道订单数据
/// </summary>
public class OtaOrderData
{
    /// <summary>
    /// 渠道单号
    /// </summary>
    public string ChannelOrderNo { get; set; }
        
    /// <summary>
    /// 订单状态
    /// </summary>
    public OtaOrderStatus Status { get; set; }

    /// <summary>
    /// 渠道类型
    /// </summary>
    public OtaChannelType OtaChannelType { get; set; }

    /// <summary>
    /// 订单金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 退款金额
    /// </summary>
    public decimal RefundAmount { get; set; }
}

/// <summary>
/// 渠道类型
/// <value>美团,携程,飞猪</value>
/// </summary>
public enum OtaChannelType
{
    /// <summary>
    /// 美团
    /// </summary>
    MeiTuan = 0,
    
    /// <summary>
    /// 携程
    /// </summary>
    Ctrip = 1,
    
    /// <summary>
    /// 飞猪
    /// </summary>
    FuiZi = 2,
}

/// <summary>
/// 渠道订单状态
/// </summary>
public enum OtaOrderStatus
{
    /// <summary>
    /// 正常
    /// </summary>
    Normal = 0,
        
    /// <summary>
    /// 取消
    /// </summary>
    Cancel = 1,
    
    /// <summary>
    /// 退款
    /// </summary>
    Refund = 2,
}

/// <summary>
/// 订单比对检查结果
/// <value>saas订单缺失,saas订单金额异常</value>
/// </summary>
public enum CheckResultType
{
    /// <summary>
    /// saas订单缺失
    /// </summary>
    SaasOrderMissing = 0,
    
    /// <summary>
    /// saas订单金额异常
    /// </summary>
    SaasOrderAmountException = 1,
}